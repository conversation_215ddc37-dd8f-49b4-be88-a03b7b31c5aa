import discord
import asyncio
import os
import aiohttp
import io

# Load environment variables FIRST before importing config
from dotenv import load_dotenv
load_dotenv()

# Configure logging to handle Unicode properly
import logging
import sys
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
# Set encoding for stdout to handle emojis
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
elif hasattr(sys.stdout, 'buffer'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)

from discord.ext import commands
from PIL import Image
from extractor import extract_deads
from util import Spreadsheet
from config import DISCORD_TOKEN, GUILD_ID, THUMBNAIL_URL, LOGS_DIR, TEMP_DIR
from model_manager import warm_up_model, get_model_stats
from webhook_logger import log_info, log_error, log_warning, log_critical
from connection_monitor import start_health_monitoring, get_health_summary
from memory_manager import start_memory_monitoring, register_cleanup_callback, get_memory_stats
from resource_pool import start_resource_manager, resource_manager
from status_dashboard import start_status_monitoring, get_quick_status, increment_processed_count

# Environment variables already loaded at the top

# Initialize bot and intents
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix="/", intents=intents)

# Near the top of your file, after your imports but before the bot initialization
from util import Spreadsheet
sheet = Spreadsheet()

# Add at the top of your file
import logging
import os
from datetime import datetime

# Set up logging using centralized configuration
log_file = LOGS_DIR / f"bot_{datetime.now().strftime('%Y%m%d')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('hoh_bot')

@bot.event
async def on_ready():
    """
    Event handler for when the bot is ready.
    """
    startup_message = f"🤖 {bot.user} is now running!"
    print(startup_message)
    log_info(startup_message, {"bot_user": str(bot.user), "guild_count": len(bot.guilds)})

    # Warm up the YOLO model for faster first request
    print("Warming up YOLO model...")
    if warm_up_model():
        success_msg = "✅ YOLO model warmed up successfully!"
        print(success_msg)
        stats = get_model_stats()
        log_info("YOLO model initialization completed", stats)
    else:
        error_msg = "❌ YOLO model warm-up failed!"
        print(error_msg)
        log_critical("YOLO model warm-up failed - bot may not function properly")

    # Test spreadsheet connection
    try:
        print(f"Testing spreadsheet connection to {sheet.filename}...")
        info = sheet.spreadsheet.sheet1.get('A1')
        success_msg = "✅ Spreadsheet connection successful!"
        print(success_msg)
        log_info("Google Sheets connection established", {"spreadsheet": sheet.filename})
    except Exception as e:
        error_msg = f"❌ Error connecting to spreadsheet: {str(e)}"
        print(error_msg)
        log_critical("Google Sheets connection failed", e, {"spreadsheet": sheet.filename})

    # Start resource management
    print("Starting resource management...")
    await start_resource_manager()

    # Register cleanup callbacks for memory management
    register_cleanup_callback(resource_manager.cleanup_resources)

    # Start memory monitoring
    print("Starting memory monitoring...")
    await start_memory_monitoring()
    log_info("Memory monitoring started", get_memory_stats())

    # Start connection health monitoring
    print("Starting connection health monitoring...")
    await start_health_monitoring()
    log_info("Health monitoring system started", get_health_summary())

    # Start status monitoring
    print("Starting status monitoring...")
    asyncio.create_task(start_status_monitoring())
    log_info("Status monitoring started")

@bot.event
async def on_disconnect():
    """Handle bot disconnection"""
    log_warning("Bot disconnected from Discord", {
        "timestamp": datetime.now().isoformat(),
        "guilds": len(bot.guilds) if bot.guilds else 0
    })

@bot.event
async def on_resumed():
    """Handle bot reconnection"""
    log_info("Bot reconnected to Discord", {
        "timestamp": datetime.now().isoformat(),
        "guilds": len(bot.guilds) if bot.guilds else 0
    })

async def send_embed(troop_data, description, channel=None, author=None, author_id=None, player_id=None):
    """
    Send a beautifully designed embedded message with dead troop information.
    Optimized for speed and visual appeal.
    """
    if not troop_data or not description:
        if channel:
            await channel.send("❌ Sorry, could not extract troop data from this image. Please try a clearer screenshot.")
        return

    # Register stats if player ID provided
    if player_id:
        sheet.register_stats(troop_data, player_id)

    # Calculate totals for dynamic design
    total_troops = sum(troop_data)

    # Dynamic color and severity based on losses
    if total_troops > 1000000:  # 1M+
        color = 0x8B0000  # Dark red
        severity_icon = "🔥"
        severity_text = "MASSIVE LOSSES"
    elif total_troops > 500000:  # 500K+
        color = 0xFF0000  # Red
        severity_icon = "💀"
        severity_text = "HEAVY LOSSES"
    elif total_troops > 100000:  # 100K+
        color = 0xFF4500  # Orange red
        severity_icon = "⚔️"
        severity_text = "SIGNIFICANT LOSSES"
    elif total_troops > 10000:  # 10K+
        color = 0xFF8C00  # Dark orange
        severity_icon = "🗡️"
        severity_text = "MODERATE LOSSES"
    else:
        color = 0xFFA500  # Orange
        severity_icon = "⚡"
        severity_text = "LIGHT LOSSES"

    # Create modern embed
    embed = discord.Embed(
        title=f"{severity_icon} {severity_text}",
        description=f"**Total Casualties:** `{total_troops:,}` troops\n\n{description}",
        color=color,
        timestamp=datetime.now()
    )

    # Add thumbnail
    embed.set_thumbnail(url=THUMBNAIL_URL)

    # Clean, minimal footer
    if author:
        footer_text = f"Reported by {author.display_name}"
        if player_id:
            footer_text += f" • Player ID: {player_id}"

        embed.set_footer(
            text=footer_text,
            icon_url=author.avatar.url if author.avatar else None
        )

    # Send the enhanced embed
    await channel.send(embed=embed)

@bot.event
async def on_message(message):
    """
    Event handler for received messages.
    Only processes images when !register command is used.
    """
    try:
        if message.author == bot.user:
            return

        # Check for !register command
        content = message.content.strip()

        # Handle !register command with player ID
        if content.lower().startswith("!register"):
            parts = content.split()

            # !register <player_id> with image
            if len(parts) == 2:
                try:
                    player_id = int(parts[1])

                    # Check if message has image attachment
                    if not message.attachments:
                        await message.channel.send(
                            f"❌ {message.author.mention}, please attach an image with your `!register {player_id}` command.\n"
                            f"**Usage:** `!register {player_id}` + attach screenshot"
                        )
                        return

                    # Process the image with the player ID
                    await process_image_with_registration(message, player_id)

                except ValueError:
                    await message.channel.send(
                        f"❌ {message.author.mention}, invalid player ID. Please provide a valid number.\n"
                        f"**Usage:** `!register <player_id>` + attach screenshot"
                    )
                    return

            # !register without player ID but with image
            elif len(parts) == 1 and message.attachments:
                # Process image without player ID registration
                await process_image_with_registration(message, None)

            # Invalid !register usage
            else:
                await message.channel.send(
                    f"❌ {message.author.mention}, invalid usage.\n"
                    f"**Usage:**\n"
                    f"• `!register <player_id>` + attach screenshot (to register stats)\n"
                    f"• `!register` + attach screenshot (just analyze, no registration)"
                )
                return

        # Ignore all other messages and images without !register command
        elif message.attachments:
            # Optional: Send a helpful message for images without command
            for attachment in message.attachments:
                if attachment.filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    await message.channel.send(
                        f"📸 {message.author.mention}, I see you uploaded an image! "
                        f"To analyze it, please use:\n"
                        f"• `!register <player_id>` + attach image (to save stats)\n"
                        f"• `!register` + attach image (just analyze)"
                    )
                    break  # Only send message once per message

    except Exception as e:
        error_msg = f"An error occurred: {str(e)}"
        await message.channel.send(error_msg)
        log_error("Message processing failed", e, {
            "user": str(message.author),
            "message_id": message.id
        })

async def process_image_with_registration(message, player_id=None):
    """
    Process image attachment with optional player registration.

    Args:
        message: Discord message with image attachment
        player_id: Optional player ID for registration
    """
    try:
        # Find image attachment
        image_attachment = None
        for attachment in message.attachments:
            if attachment.filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_attachment = attachment
                break

        if not image_attachment:
            await message.channel.send("❌ No valid image found. Please attach a PNG, JPG, or JPEG file.")
            return

        # Show processing message
        if player_id:
            processing_msg = await message.channel.send(
                f"⚡ Processing {message.author.mention}'s image and registering to Player ID {player_id}..."
            )
        else:
            processing_msg = await message.channel.send(
                f"⚡ Analyzing {message.author.mention}'s image..."
            )

        # Optimized image loading and processing
        image_bytes = await image_attachment.read()

        with Image.open(io.BytesIO(image_bytes)) as image:
            # Convert to RGB if needed (reduces memory)
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Process directly (fast path)
            troop_data, description, processed_image = extract_deads(image)

        # Clear image bytes from memory
        del image_bytes

        # Handle processing results
        if troop_data is None or description is None or len(troop_data) == 0:
            await processing_msg.edit(content="❌ Could not extract troop data. Please try a clearer screenshot.")
            log_warning("Failed to extract troop data from image", {
                "user": str(message.author),
                "message_id": message.id,
                "filename": image_attachment.filename,
                "player_id": player_id
            })
            return

        # Send beautiful embed (primary result)
        await send_embed(troop_data, description, message.channel, message.author, message.author.id, player_id)

        # Update processing message to success
        if player_id:
            await processing_msg.edit(
                content=f"✅ Successfully processed and registered {message.author.mention}'s stats to Player ID {player_id}!"
            )
        else:
            await processing_msg.edit(
                content=f"✅ Successfully analyzed {message.author.mention}'s image!"
            )

        # Increment processed count for statistics
        increment_processed_count()

    except Exception as e:
        error_msg = f"Error processing image: {str(e)}"
        await message.channel.send(error_msg)
        log_error("Image processing failed", e, {
            "user": str(message.author),
            "message_id": message.id,
            "filename": image_attachment.filename if 'image_attachment' in locals() else "unknown",
            "player_id": player_id
        })

async def main():
    """
    Main function to start the bot with auto-reconnection.
    """
    max_retries = 5
    retry_delay = 5  # seconds

    for attempt in range(max_retries):
        try:
            async with bot:
                log_info(f"Starting bot (attempt {attempt + 1}/{max_retries})")
                await bot.start(DISCORD_TOKEN, reconnect=True)
                break  # If we get here, bot started successfully

        except Exception as e:
            log_error(f"Bot startup failed (attempt {attempt + 1}/{max_retries})", e)

            if attempt < max_retries - 1:
                print(f"Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                log_critical("Bot failed to start after all retry attempts", e)
                raise

# Run the bot
if __name__ == "__main__":
    asyncio.run(main())
