2025-03-27 06:13:13,612 - discord.client - INFO - logging in using static token
2025-03-27 06:13:14,362 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 13485037a90771c017e945fc4859f69f).
2025-03-27 06:56:33,024 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 13485037a90771c017e945fc4859f69f.
2025-03-27 07:41:00,293 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 13485037a90771c017e945fc4859f69f.
2025-03-27 09:24:19,382 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:24:29,389 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:24:39,393 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:24:46,953 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 13485037a90771c017e945fc4859f69f.
2025-03-27 09:25:38,145 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:25:48,152 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:25:58,161 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:26:08,169 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:26:18,175 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:26:28,181 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 60 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 277, in run_and_get_output
    with save(image) as (temp_name, input_filename):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 202, in save
    cleanup(f.name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 163, in cleanup
    remove(filename)

2025-03-27 09:26:31,448 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 13485037a90771c017e945fc4859f69f.
2025-03-27 09:56:15,529 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:56:25,539 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:56:35,548 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:56:41,983 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 13485037a90771c017e945fc4859f69f.
2025-03-27 09:58:14,448 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:58:24,462 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:58:34,469 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1519, in _communicate
    self.stderr_thread.start()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 940, in start
    self._started.wait()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 607, in wait
    signaled = self._cond.wait(timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 320, in wait
    waiter.acquire()

2025-03-27 09:58:44,487 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 09:58:48,978 - discord.client - ERROR - Attempting a reconnect in 0.99s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-03-27 09:58:50,297 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 13485037a90771c017e945fc4859f69f.
2025-03-27 10:57:27,200 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 37, in extract_deads
    print(f"Tiers dictionary: {tiers}")

2025-03-27 11:30:38,616 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 40, in extract_deads
    results = model(image)[0]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ultralytics\engine\model.py", line 98, in __call__
    return self.predict(source, stream, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\torch\utils\_contextlib.py", line 115, in decorate_context
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ultralytics\engine\model.py", line 246, in predict
    return self.predictor.predict_cli(source=source) if is_cli else self.predictor(source=source, stream=stream)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ultralytics\engine\predictor.py", line 197, in __call__
    return list(self.stream_inference(source, model, *args, **kwargs))  # merge list of Result into one
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\torch\utils\_contextlib.py", line 35, in generator_context
    response = gen.send(None)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ultralytics\engine\predictor.py", line 222, in stream_inference
    LOGGER.info('')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\logging\__init__.py", line 1477, in info
    self._log(INFO, msg, args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\logging\__init__.py", line 1624, in _log
    self.handle(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\logging\__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\logging\__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\logging\__init__.py", line 968, in handle
    self.emit(record)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\logging\__init__.py", line 1103, in emit
    stream.write(msg + self.terminator)

2025-03-27 11:30:55,138 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 11:31:05,144 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 11:31:15,151 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 11:31:25,164 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 60 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 11:31:35,177 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 70 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 11:31:45,200 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 80 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 11:31:53,349 - discord.gateway - INFO - Shard ID None session has been invalidated.
2025-03-27 11:31:58,753 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 990b441f2a7d18d6be9dd9ef2df6b52f).
2025-03-27 11:34:07,246 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 11:34:17,254 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1173, in communicate
    sts = self.wait(timeout=self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1209, in wait
    return self._wait(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1490, in _wait
    result = _winapi.WaitForSingleObject(self._handle,

2025-03-27 11:34:27,261 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 11:34:34,537 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-27 13:39:36,850 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-27 14:19:42,281 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-27 15:46:37,429 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-27 19:01:44,882 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-27 19:12:13,719 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1512, in _communicate
    self.stdout_thread.start()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 940, in start
    self._started.wait()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 607, in wait
    signaled = self._cond.wait(timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 320, in wait
    waiter.acquire()

2025-03-27 19:12:23,744 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 19:12:33,752 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 19:12:43,757 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 19:12:53,765 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 19:13:03,770 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 60 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 19:13:10,896 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-27 19:14:43,364 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 19:14:53,372 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 19:15:03,379 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 19:15:13,385 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-27 19:15:23,533 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 136, in on_message
    processed_image.save(temp_image_path)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PIL\Image.py", line 2413, in save
    save_handler(self, fp, filename)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PIL\PngImagePlugin.py", line 1398, in _save
    ImageFile._save(im, _idat(fp, chunk), [("zip", (0, 0) + im.size, 0, rawmode)])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PIL\ImageFile.py", line 519, in _save
    _encode_tile(im, fp, tile, bufsize, None, exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PIL\ImageFile.py", line 538, in _encode_tile
    errcode, data = encoder.encode(bufsize)[1:]

2025-03-27 19:15:24,531 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-27 19:35:57,380 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-27 21:40:41,072 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-28 00:34:18,348 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-28 01:43:11,091 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-28 02:40:37,170 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-28 03:31:23,164 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-28 06:20:27,535 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-28 09:18:24,794 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-28 10:42:03,955 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-28 11:50:08,666 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-28 12:36:22,762 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-28 12:36:30,244 - discord.client - ERROR - Attempting a reconnect in 1.97s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-03-28 12:36:32,372 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-28 12:38:04,834 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-28 12:38:11,036 - discord.client - ERROR - Attempting a reconnect in 1.89s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-03-28 12:38:13,080 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
2025-03-28 13:20:15,876 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 990b441f2a7d18d6be9dd9ef2df6b52f.
