"""
Model Manager for Hall of Heroes Bot
Implements singleton pattern for YOLO model caching and management
"""

import threading
import time
import gc
from typing import Optional
from ultralytics import YOLO
from config import YOLO_MODEL_PATH, MODEL_CACHE_ENABLED
import logging

logger = logging.getLogger(__name__)

class ModelManager:
    """
    Singleton class to manage YOLO model loading and caching
    Ensures only one model instance is loaded in memory
    """
    
    _instance: Optional['ModelManager'] = None
    _lock = threading.Lock()
    
    def __new__(cls) -> 'ModelManager':
        """Singleton pattern implementation"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the model manager"""
        if self._initialized:
            return
            
        self._model: Optional[YOLO] = None
        self._model_lock = threading.Lock()
        self._last_used = 0
        self._load_count = 0
        self._initialized = True
        
        logger.info("ModelManager initialized")
    
    def get_model(self) -> YOLO:
        """
        Get the YOLO model instance (loads if not cached)
        
        Returns:
            YOLO: The loaded YOLO model
        """
        if not MODEL_CACHE_ENABLED:
            # If caching is disabled, load fresh model each time
            logger.debug("Model caching disabled, loading fresh model")
            return YOLO(str(YOLO_MODEL_PATH))
        
        with self._model_lock:
            if self._model is None:
                logger.info(f"Loading YOLO model from {YOLO_MODEL_PATH}")
                start_time = time.time()
                
                try:
                    self._model = YOLO(str(YOLO_MODEL_PATH))
                    load_time = time.time() - start_time
                    self._load_count += 1
                    
                    logger.info(f"YOLO model loaded successfully in {load_time:.2f}s (load #{self._load_count})")
                    
                except Exception as e:
                    logger.error(f"Failed to load YOLO model: {str(e)}")
                    raise
            
            self._last_used = time.time()
            return self._model
    
    def warm_up(self) -> bool:
        """
        Pre-load the model to avoid first-request delay
        
        Returns:
            bool: True if warm-up successful, False otherwise
        """
        try:
            logger.info("Warming up YOLO model...")
            model = self.get_model()
            
            # Run a dummy inference to fully initialize the model
            import numpy as np
            from PIL import Image
            
            # Create a small dummy image
            dummy_image = Image.fromarray(np.zeros((100, 100, 3), dtype=np.uint8))
            
            start_time = time.time()
            _ = model(dummy_image)
            warm_up_time = time.time() - start_time
            
            logger.info(f"Model warm-up completed in {warm_up_time:.2f}s")
            return True
            
        except Exception as e:
            logger.error(f"Model warm-up failed: {str(e)}")
            return False
    
    def clear_cache(self) -> None:
        """Clear the cached model to free memory"""
        with self._model_lock:
            if self._model is not None:
                logger.info("Clearing model cache")
                del self._model
                self._model = None
                gc.collect()  # Force garbage collection
                logger.info("Model cache cleared")
    
    def get_stats(self) -> dict:
        """
        Get model manager statistics
        
        Returns:
            dict: Statistics about model usage
        """
        return {
            'model_loaded': self._model is not None,
            'load_count': self._load_count,
            'last_used': self._last_used,
            'cache_enabled': MODEL_CACHE_ENABLED,
            'model_path': str(YOLO_MODEL_PATH)
        }
    
    def is_model_loaded(self) -> bool:
        """Check if model is currently loaded in memory"""
        return self._model is not None
    
    def reload_model(self) -> bool:
        """
        Force reload the model (useful for model updates)
        
        Returns:
            bool: True if reload successful, False otherwise
        """
        try:
            logger.info("Force reloading YOLO model")
            self.clear_cache()
            model = self.get_model()
            logger.info("Model reloaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Model reload failed: {str(e)}")
            return False

# Global model manager instance
model_manager = ModelManager()

def get_model() -> YOLO:
    """
    Convenience function to get the YOLO model
    
    Returns:
        YOLO: The loaded YOLO model
    """
    return model_manager.get_model()

def warm_up_model() -> bool:
    """
    Convenience function to warm up the model
    
    Returns:
        bool: True if warm-up successful, False otherwise
    """
    return model_manager.warm_up()

def clear_model_cache() -> None:
    """Convenience function to clear model cache"""
    model_manager.clear_cache()

def get_model_stats() -> dict:
    """
    Convenience function to get model statistics
    
    Returns:
        dict: Model manager statistics
    """
    return model_manager.get_stats()
