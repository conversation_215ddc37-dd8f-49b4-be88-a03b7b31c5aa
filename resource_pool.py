"""
Resource Pooling System for Hall of Heroes Bot
Manages object pools for expensive resources
"""

import asyncio
import threading
import time
import weakref
from typing import Dict, List, Optional, TypeVar, Generic, Callable, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

from config import IMAGE_CACHE_SIZE
from webhook_logger import log_info, log_warning

logger = logging.getLogger(__name__)

T = TypeVar('T')

@dataclass
class PooledResource:
    """Wrapper for pooled resources"""
    resource: Any
    created_at: datetime
    last_used: datetime
    use_count: int
    in_use: bool = False

class ResourcePool(Generic[T]):
    """
    Generic resource pool implementation
    """
    
    def __init__(self, 
                 factory: Callable[[], T],
                 cleanup: Optional[Callable[[T], None]] = None,
                 max_size: int = 10,
                 max_age: timedelta = timedelta(hours=1),
                 max_idle: timedelta = timedelta(minutes=30)):
        """
        Initialize resource pool
        
        Args:
            factory: Function to create new resources
            cleanup: Function to cleanup resources (optional)
            max_size: Maximum pool size
            max_age: Maximum age of resources
            max_idle: Maximum idle time before cleanup
        """
        self._factory = factory
        self._cleanup = cleanup
        self._max_size = max_size
        self._max_age = max_age
        self._max_idle = max_idle
        
        self._pool: List[PooledResource] = []
        self._lock = threading.Lock()
        self._total_created = 0
        self._total_reused = 0
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
    
    def start(self):
        """Start the resource pool"""
        if not self._running:
            self._running = True
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.debug(f"Resource pool started (max_size={self._max_size})")
    
    async def stop(self):
        """Stop the resource pool and cleanup all resources"""
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Cleanup all resources
        with self._lock:
            for pooled in self._pool:
                if self._cleanup:
                    try:
                        self._cleanup(pooled.resource)
                    except Exception as e:
                        logger.error(f"Error cleaning up resource: {e}")
            self._pool.clear()
        
        logger.debug("Resource pool stopped")
    
    def acquire(self) -> T:
        """Acquire a resource from the pool"""
        with self._lock:
            # Try to find an available resource
            for pooled in self._pool:
                if not pooled.in_use:
                    pooled.in_use = True
                    pooled.last_used = datetime.now()
                    pooled.use_count += 1
                    self._total_reused += 1
                    logger.debug(f"Reused resource (use_count={pooled.use_count})")
                    return pooled.resource
            
            # No available resource, create new one if under limit
            if len(self._pool) < self._max_size:
                try:
                    resource = self._factory()
                    pooled = PooledResource(
                        resource=resource,
                        created_at=datetime.now(),
                        last_used=datetime.now(),
                        use_count=1,
                        in_use=True
                    )
                    self._pool.append(pooled)
                    self._total_created += 1
                    logger.debug(f"Created new resource (pool_size={len(self._pool)})")
                    return resource
                except Exception as e:
                    logger.error(f"Failed to create resource: {e}")
                    raise
            
            # Pool is full, wait for a resource to become available
            # For now, create a temporary resource (not pooled)
            logger.warning("Resource pool exhausted, creating temporary resource")
            return self._factory()
    
    def release(self, resource: T):
        """Release a resource back to the pool"""
        with self._lock:
            for pooled in self._pool:
                if pooled.resource is resource:
                    pooled.in_use = False
                    pooled.last_used = datetime.now()
                    logger.debug(f"Released resource back to pool")
                    return
            
            # Resource not in pool (temporary resource)
            if self._cleanup:
                try:
                    self._cleanup(resource)
                    logger.debug("Cleaned up temporary resource")
                except Exception as e:
                    logger.error(f"Error cleaning up temporary resource: {e}")
    
    async def _cleanup_loop(self):
        """Cleanup loop for expired resources"""
        while self._running:
            try:
                await self._cleanup_expired()
                await asyncio.sleep(60)  # Check every minute
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in resource pool cleanup: {e}")
                await asyncio.sleep(5)
    
    async def _cleanup_expired(self):
        """Remove expired resources from pool"""
        now = datetime.now()
        to_remove = []
        
        with self._lock:
            for i, pooled in enumerate(self._pool):
                if pooled.in_use:
                    continue
                
                # Check if resource is too old
                if now - pooled.created_at > self._max_age:
                    to_remove.append(i)
                    continue
                
                # Check if resource has been idle too long
                if now - pooled.last_used > self._max_idle:
                    to_remove.append(i)
                    continue
            
            # Remove expired resources (in reverse order to maintain indices)
            for i in reversed(to_remove):
                pooled = self._pool.pop(i)
                if self._cleanup:
                    try:
                        self._cleanup(pooled.resource)
                    except Exception as e:
                        logger.error(f"Error cleaning up expired resource: {e}")
        
        if to_remove:
            logger.debug(f"Cleaned up {len(to_remove)} expired resources")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get pool statistics"""
        with self._lock:
            in_use = sum(1 for p in self._pool if p.in_use)
            available = len(self._pool) - in_use
            
            return {
                "total_size": len(self._pool),
                "in_use": in_use,
                "available": available,
                "max_size": self._max_size,
                "total_created": self._total_created,
                "total_reused": self._total_reused,
                "reuse_ratio": self._total_reused / max(1, self._total_created + self._total_reused)
            }

class ImageCache:
    """
    Specialized cache for processed images
    """
    
    def __init__(self, max_size: int = IMAGE_CACHE_SIZE):
        self._cache: Dict[str, Any] = {}
        self._access_times: Dict[str, datetime] = {}
        self._max_size = max_size
        self._lock = threading.Lock()
        self._hits = 0
        self._misses = 0
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        with self._lock:
            if key in self._cache:
                self._access_times[key] = datetime.now()
                self._hits += 1
                return self._cache[key]
            else:
                self._misses += 1
                return None
    
    def put(self, key: str, value: Any):
        """Put item in cache"""
        with self._lock:
            # If cache is full, remove least recently used item
            if len(self._cache) >= self._max_size and key not in self._cache:
                lru_key = min(self._access_times.keys(), 
                             key=lambda k: self._access_times[k])
                del self._cache[lru_key]
                del self._access_times[lru_key]
            
            self._cache[key] = value
            self._access_times[key] = datetime.now()
    
    def clear(self):
        """Clear the cache"""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            total_requests = self._hits + self._misses
            hit_rate = self._hits / max(1, total_requests)
            
            return {
                "size": len(self._cache),
                "max_size": self._max_size,
                "hits": self._hits,
                "misses": self._misses,
                "hit_rate": hit_rate,
                "total_requests": total_requests
            }

class ResourceManager:
    """
    Central resource manager
    """
    
    def __init__(self):
        self._pools: Dict[str, ResourcePool] = {}
        self._image_cache = ImageCache()
        self._running = False
    
    def create_pool(self, name: str, factory: Callable, 
                   cleanup: Optional[Callable] = None,
                   max_size: int = 10) -> ResourcePool:
        """Create a new resource pool"""
        pool = ResourcePool(factory, cleanup, max_size)
        self._pools[name] = pool
        if self._running:
            pool.start()
        return pool
    
    def get_pool(self, name: str) -> Optional[ResourcePool]:
        """Get a resource pool by name"""
        return self._pools.get(name)
    
    def get_image_cache(self) -> ImageCache:
        """Get the image cache"""
        return self._image_cache
    
    async def start(self):
        """Start all resource pools"""
        self._running = True
        for pool in self._pools.values():
            pool.start()
        log_info("Resource manager started", {
            "pools": list(self._pools.keys()),
            "image_cache_size": self._image_cache._max_size
        })
    
    async def stop(self):
        """Stop all resource pools"""
        self._running = False
        for pool in self._pools.values():
            await pool.stop()
        self._image_cache.clear()
        log_info("Resource manager stopped")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics for all resources"""
        stats = {
            "pools": {},
            "image_cache": self._image_cache.get_stats()
        }
        
        for name, pool in self._pools.items():
            stats["pools"][name] = pool.get_stats()
        
        return stats
    
    def cleanup_resources(self):
        """Cleanup callback for memory manager"""
        self._image_cache.clear()
        log_info("Resource cleanup performed", self.get_stats())

# Global resource manager
resource_manager = ResourceManager()

# Convenience functions
def create_resource_pool(name: str, factory: Callable, 
                        cleanup: Optional[Callable] = None,
                        max_size: int = 10) -> ResourcePool:
    """Create a resource pool"""
    return resource_manager.create_pool(name, factory, cleanup, max_size)

def get_resource_pool(name: str) -> Optional[ResourcePool]:
    """Get a resource pool"""
    return resource_manager.get_pool(name)

def get_image_cache() -> ImageCache:
    """Get the image cache"""
    return resource_manager.get_image_cache()

async def start_resource_manager():
    """Start the resource manager"""
    await resource_manager.start()

async def stop_resource_manager():
    """Stop the resource manager"""
    await resource_manager.stop()

def get_resource_stats() -> Dict[str, Any]:
    """Get resource statistics"""
    return resource_manager.get_stats()
