"""
Concurrent Processing System for Hall of Heroes Bot
Handles multiple image processing requests safely and efficiently
"""

import asyncio
import threading
import time
import uuid
from typing import Dict, List, Optional, Any, Callable, <PERSON><PERSON>
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import logging

from config import MAX_CONCURRENT_PROCESSES, IMAGE_PROCESSING_TIMEOUT
from webhook_logger import log_info, log_warning, log_error
from performance_monitor import PerformanceTimer

logger = logging.getLogger(__name__)

class ProcessingStatus(Enum):
    """Processing status states"""
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"

@dataclass
class ProcessingJob:
    """Processing job data"""
    job_id: str
    user_id: str
    message_id: str
    image_data: Any
    player_id: Optional[int]
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: ProcessingStatus = ProcessingStatus.QUEUED
    result: Optional[Tuple] = None
    error: Optional[str] = None
    priority: int = 0  # Higher number = higher priority

class ProcessingQueue:
    """
    Thread-safe processing queue with priority support
    """
    
    def __init__(self, max_size: int = 100):
        self._queue: List[ProcessingJob] = []
        self._max_size = max_size
        self._lock = threading.Lock()
        self._total_queued = 0
        self._total_processed = 0
    
    def add_job(self, job: ProcessingJob) -> bool:
        """Add a job to the queue"""
        with self._lock:
            if len(self._queue) >= self._max_size:
                return False

            # Insert job in priority order (higher priority first)
            inserted = False
            for i, existing_job in enumerate(self._queue):
                if job.priority > existing_job.priority:
                    self._queue.insert(i, job)
                    inserted = True
                    break

            if not inserted:
                self._queue.append(job)

            self._total_queued += 1
            return True
    
    def get_job(self) -> Optional[ProcessingJob]:
        """Get the next job from the queue (non-blocking)"""
        with self._lock:
            if not self._queue:
                return None

            job = self._queue.pop(0)
            job.status = ProcessingStatus.PROCESSING
            job.started_at = datetime.now()
            return job
    
    def get_job_by_id(self, job_id: str) -> Optional[ProcessingJob]:
        """Get a specific job by ID"""
        with self._lock:
            for job in self._queue:
                if job.job_id == job_id:
                    return job
            return None
    
    def remove_job(self, job_id: str) -> bool:
        """Remove a job from the queue"""
        with self._lock:
            for i, job in enumerate(self._queue):
                if job.job_id == job_id:
                    job.status = ProcessingStatus.CANCELLED
                    self._queue.pop(i)
                    return True
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get queue statistics"""
        with self._lock:
            status_counts = {}
            for status in ProcessingStatus:
                status_counts[status.value] = sum(
                    1 for job in self._queue if job.status == status
                )
            
            return {
                "queue_size": len(self._queue),
                "max_size": self._max_size,
                "total_queued": self._total_queued,
                "total_processed": self._total_processed,
                "status_counts": status_counts
            }

class ConcurrentProcessor:
    """
    Manages concurrent image processing with thread safety
    """
    
    def __init__(self, max_workers: int = MAX_CONCURRENT_PROCESSES):
        self._max_workers = max_workers
        self._workers: List[asyncio.Task] = []
        self._queue = ProcessingQueue()
        self._active_jobs: Dict[str, ProcessingJob] = {}
        self._completed_jobs: Dict[str, ProcessingJob] = {}
        self._max_completed_history = 100
        self._lock = threading.Lock()
        self._running = False
        
        # Statistics
        self._total_processed = 0
        self._total_failed = 0
        self._total_timeout = 0
        self._avg_processing_time = 0.0
    
    async def start(self):
        """Start the concurrent processor"""
        if self._running:
            return
        
        self._running = True
        
        # Start worker tasks
        for i in range(self._max_workers):
            worker = asyncio.create_task(self._worker_loop(f"worker-{i}"))
            self._workers.append(worker)
        
        log_info("Concurrent processor started", {
            "max_workers": self._max_workers,
            "queue_max_size": self._queue._max_size
        })
    
    async def stop(self):
        """Stop the concurrent processor"""
        self._running = False
        
        # Cancel all workers
        for worker in self._workers:
            worker.cancel()
        
        # Wait for workers to finish
        if self._workers:
            await asyncio.gather(*self._workers, return_exceptions=True)
        
        self._workers.clear()
        log_info("Concurrent processor stopped")
    
    async def submit_job(self, user_id: str, message_id: str, image_data: Any,
                        player_id: Optional[int] = None, priority: int = 0) -> str:
        """
        Submit a processing job
        
        Returns:
            str: Job ID for tracking
        """
        job_id = str(uuid.uuid4())
        
        job = ProcessingJob(
            job_id=job_id,
            user_id=user_id,
            message_id=message_id,
            image_data=image_data,
            player_id=player_id,
            created_at=datetime.now(),
            priority=priority
        )
        
        if self._queue.add_job(job):
            with self._lock:
                self._active_jobs[job_id] = job
            
            log_info("Processing job submitted", {
                "job_id": job_id[:8],
                "user_id": user_id,
                "message_id": message_id,
                "priority": priority,
                "queue_size": len(self._queue._queue)
            })
            
            return job_id
        else:
            raise Exception("Processing queue is full")
    
    async def get_job_status(self, job_id: str) -> Optional[ProcessingJob]:
        """Get the status of a job"""
        with self._lock:
            # Check active jobs
            if job_id in self._active_jobs:
                return self._active_jobs[job_id]
            
            # Check completed jobs
            if job_id in self._completed_jobs:
                return self._completed_jobs[job_id]
        
        return None
    
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a job"""
        # Try to remove from queue first
        if self._queue.remove_job(job_id):
            with self._lock:
                if job_id in self._active_jobs:
                    job = self._active_jobs.pop(job_id)
                    job.status = ProcessingStatus.CANCELLED
                    self._completed_jobs[job_id] = job
            return True
        
        return False
    
    async def _worker_loop(self, worker_name: str):
        """Main worker loop"""
        logger.debug(f"Worker {worker_name} started")

        while self._running:
            try:
                # Get next job from queue (non-blocking)
                job = self._queue.get_job()
                if not job:
                    # No jobs available, sleep briefly and check again
                    await asyncio.sleep(1.0)
                    continue

                # Process the job
                await self._process_job(job, worker_name)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in worker {worker_name}: {e}")
                await asyncio.sleep(1)

        logger.debug(f"Worker {worker_name} stopped")
    
    async def _process_job(self, job: ProcessingJob, worker_name: str):
        """Process a single job"""
        start_time = time.time()
        
        try:
            with PerformanceTimer(f"concurrent_processing_{worker_name}"):
                # Import here to avoid circular imports
                from extractor import extract_deads
                
                # Process the image with timeout
                result = await asyncio.wait_for(
                    asyncio.get_event_loop().run_in_executor(
                        None, extract_deads, job.image_data
                    ),
                    timeout=IMAGE_PROCESSING_TIMEOUT
                )
                
                # Job completed successfully
                job.result = result
                job.status = ProcessingStatus.COMPLETED
                job.completed_at = datetime.now()
                
                processing_time = time.time() - start_time
                self._update_stats(processing_time, success=True)
                
                log_info("Processing job completed", {
                    "job_id": job.job_id[:8],
                    "worker": worker_name,
                    "processing_time": processing_time,
                    "user_id": job.user_id
                })
                
        except asyncio.TimeoutError:
            job.status = ProcessingStatus.TIMEOUT
            job.error = f"Processing timeout after {IMAGE_PROCESSING_TIMEOUT}s"
            job.completed_at = datetime.now()
            
            self._update_stats(time.time() - start_time, timeout=True)
            
            log_warning("Processing job timeout", {
                "job_id": job.job_id[:8],
                "worker": worker_name,
                "timeout": IMAGE_PROCESSING_TIMEOUT
            })
            
        except Exception as e:
            job.status = ProcessingStatus.FAILED
            job.error = str(e)
            job.completed_at = datetime.now()
            
            self._update_stats(time.time() - start_time, success=False)
            
            log_error("Processing job failed", e, {
                "job_id": job.job_id[:8],
                "worker": worker_name,
                "user_id": job.user_id
            })
        
        finally:
            # Move job from active to completed
            with self._lock:
                if job.job_id in self._active_jobs:
                    self._active_jobs.pop(job.job_id)
                
                self._completed_jobs[job.job_id] = job
                
                # Limit completed job history
                if len(self._completed_jobs) > self._max_completed_history:
                    oldest_job_id = min(self._completed_jobs.keys(),
                                       key=lambda k: self._completed_jobs[k].completed_at)
                    self._completed_jobs.pop(oldest_job_id)
    
    def _update_stats(self, processing_time: float, success: bool = True, 
                     timeout: bool = False):
        """Update processing statistics"""
        with self._lock:
            self._total_processed += 1
            
            if success:
                # Update average processing time
                if self._avg_processing_time == 0:
                    self._avg_processing_time = processing_time
                else:
                    self._avg_processing_time = (
                        (self._avg_processing_time * (self._total_processed - 1) + processing_time) 
                        / self._total_processed
                    )
            elif timeout:
                self._total_timeout += 1
            else:
                self._total_failed += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processor statistics"""
        with self._lock:
            queue_stats = self._queue.get_stats()
            
            return {
                "workers": {
                    "max_workers": self._max_workers,
                    "active_workers": len([w for w in self._workers if not w.done()]),
                    "total_workers": len(self._workers)
                },
                "jobs": {
                    "active": len(self._active_jobs),
                    "completed_history": len(self._completed_jobs),
                    "total_processed": self._total_processed,
                    "total_failed": self._total_failed,
                    "total_timeout": self._total_timeout
                },
                "performance": {
                    "avg_processing_time": self._avg_processing_time,
                    "success_rate": (self._total_processed - self._total_failed - self._total_timeout) / max(1, self._total_processed)
                },
                "queue": queue_stats
            }

# Global concurrent processor
concurrent_processor = ConcurrentProcessor()

# Convenience functions
async def start_concurrent_processing():
    """Start concurrent processing"""
    await concurrent_processor.start()

async def stop_concurrent_processing():
    """Stop concurrent processing"""
    await concurrent_processor.stop()

async def submit_processing_job(user_id: str, message_id: str, image_data: Any,
                               player_id: Optional[int] = None, priority: int = 0) -> str:
    """Submit a processing job"""
    return await concurrent_processor.submit_job(user_id, message_id, image_data, player_id, priority)

async def get_processing_status(job_id: str) -> Optional[ProcessingJob]:
    """Get processing job status"""
    return await concurrent_processor.get_job_status(job_id)

async def cancel_processing_job(job_id: str) -> bool:
    """Cancel a processing job"""
    return await concurrent_processor.cancel_job(job_id)

def get_processing_stats() -> Dict[str, Any]:
    """Get processing statistics"""
    return concurrent_processor.get_stats()
