"""
Configuration file for Hall of Heroes Discord Bot
Centralizes all configuration to prevent inconsistencies
"""

import os
from pathlib import Path
from typing import Dict, List

# Base paths
PROJECT_ROOT = Path(__file__).parent
WEIGHTS_DIR = PROJECT_ROOT / "weights"
LOGS_DIR = PROJECT_ROOT / "logs"
TEMP_DIR = PROJECT_ROOT / "temp"

# Ensure directories exist
LOGS_DIR.mkdir(exist_ok=True)
TEMP_DIR.mkdir(exist_ok=True)

# Discord Configuration
DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
GUILD_ID = os.getenv('GUILD_ID')
THUMBNAIL_URL = "https://media.discordapp.net/attachments/1313178325291761677/1315091550652334142/IMG_3625.png?ex=675625f2&is=6754d472&hm=350612555ff03f90dddca533b55b3f04948a0c61a6a11ee26d0fc1caab93042e&=&format=webp&quality=lossless&width=468&height=468"

# Google Sheets Configuration
SPREADSHEET_NAME = "KvK Discord Bot Stats2"
SHEET_NAME = "Hall of Heroes"
SERVICE_ACCOUNT_FILE = PROJECT_ROOT / "service-account.json"

# Model Configuration
YOLO_MODEL_PATH = WEIGHTS_DIR / "best.pt"
TESSERACT_CMD = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Troop Configuration - SINGLE SOURCE OF TRUTH
TIER_MAPPING = {5: 'T1', 6: 'T2', 7: 'T3', 8: 'T4', 9: 'T5'}
TYPE_MAPPING = {1: 'Infantry', 2: 'Cavalry', 3: 'Archers', 4: 'Siege'}

# YOLO Class IDs
TROOP_TIER_CLASSES = [5, 6, 7, 8, 9]
TROOP_TYPE_CLASSES = [1, 2, 3, 4]
AMOUNT_CLASSES = [0]

# Spreadsheet column order (matches TYPE_MAPPING keys)
SPREADSHEET_TYPE_ORDER = [1, 2, 3, 4]  # Infantry, Cavalry, Archers, Siege
SPREADSHEET_TIER_ORDER = [5, 6, 7, 8, 9]  # T1, T2, T3, T4, T5

# Generate spreadsheet headers
def get_spreadsheet_headers() -> List[str]:
    """Generate standardized spreadsheet headers"""
    headers = ['Player ID']
    for tier_id in SPREADSHEET_TIER_ORDER:
        tier_name = TIER_MAPPING[tier_id]
        for type_id in SPREADSHEET_TYPE_ORDER:
            type_name = TYPE_MAPPING[type_id]
            headers.append(f"{tier_name} {type_name}")
    return headers

# Processing Configuration (Optimized for Speed)
MAX_CONCURRENT_PROCESSES = 2  # Reduced for stability
IMAGE_PROCESSING_TIMEOUT = 60  # seconds (optimized)
OCR_TIMEOUT = 15  # seconds (optimized)
ENABLE_DETECTION_IMAGES = False  # Disable to save bandwidth/memory
EMBED_OPTIMIZATION = True  # Enable optimized embed design

# Performance Configuration
MODEL_CACHE_ENABLED = True
OCR_CACHE_ENABLED = True
CACHE_TTL = 3600  # 1 hour

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_ROTATION_SIZE = "10MB"
LOG_RETENTION_DAYS = 30

# Error Handling Configuration
MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY = 1.0  # seconds
CIRCUIT_BREAKER_THRESHOLD = 5
CIRCUIT_BREAKER_TIMEOUT = 60  # seconds

# Validation Configuration
MIN_TROOP_COUNT = 0
MAX_TROOP_COUNT = 10000000  # 10 million
CONFIDENCE_THRESHOLD = 0.7
MANUAL_REVIEW_THRESHOLD = 0.5

# Memory Management
MAX_MEMORY_USAGE_MB = 1024  # 1GB
GARBAGE_COLLECTION_INTERVAL = 300  # 5 minutes
IMAGE_CACHE_SIZE = 100  # number of images

# Discord Webhook Configuration (for logging)
ERROR_WEBHOOK_URL = os.getenv('ERROR_WEBHOOK_URL')
STATUS_WEBHOOK_URL = os.getenv('STATUS_WEBHOOK_URL')
REVIEW_WEBHOOK_URL = os.getenv('REVIEW_WEBHOOK_URL')

# Webhook rate limiting (seconds between messages)
WEBHOOK_RATE_LIMITS = {
    'error': 5,    # 5 seconds between error messages
    'status': 30,  # 30 seconds between status messages
    'review': 10,  # 10 seconds between review messages
}

# Health Check Configuration
HEALTH_CHECK_INTERVAL = 60  # seconds
CONNECTION_TIMEOUT = 30  # seconds
HEARTBEAT_INTERVAL = 30  # seconds
