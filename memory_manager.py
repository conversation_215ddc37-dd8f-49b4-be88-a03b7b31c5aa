"""
Memory Management System for Hall of Heroes Bot
Monitors memory usage and implements automatic cleanup
"""

import gc
import psutil
import threading
import time
import asyncio
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

from config import (
    MAX_MEMORY_USAGE_MB, GARBAGE_COLLECTION_INTERVAL,
    IMAGE_CACHE_SIZE
)
from webhook_logger import log_warning, log_error, log_info

logger = logging.getLogger(__name__)

@dataclass
class MemorySnapshot:
    """Memory usage snapshot"""
    timestamp: datetime
    rss_mb: float
    vms_mb: float
    percent: float
    available_mb: float
    gc_collections: Dict[int, int]

class MemoryManager:
    """
    Memory management and monitoring system
    """
    
    def __init__(self):
        self._process = psutil.Process()
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
        self._cleanup_callbacks: List[Callable] = []
        self._memory_history: List[MemorySnapshot] = []
        self._max_history = 100
        self._lock = threading.Lock()
        
        # Memory thresholds
        self._warning_threshold = MAX_MEMORY_USAGE_MB * 0.8  # 80%
        self._critical_threshold = MAX_MEMORY_USAGE_MB * 0.9  # 90%
        self._emergency_threshold = MAX_MEMORY_USAGE_MB  # 100%
        
        # Cleanup state
        self._last_gc_time = 0
        self._last_warning_time = 0
        self._cleanup_in_progress = False
    
    def register_cleanup_callback(self, callback: Callable) -> None:
        """Register a cleanup callback function"""
        self._cleanup_callbacks.append(callback)
        logger.debug(f"Registered cleanup callback: {callback.__name__}")
    
    def get_memory_usage(self) -> MemorySnapshot:
        """Get current memory usage snapshot"""
        try:
            memory_info = self._process.memory_info()
            memory_percent = self._process.memory_percent()
            system_memory = psutil.virtual_memory()
            
            # Get garbage collection stats
            gc_stats = {}
            for i in range(3):  # Python has 3 GC generations
                gc_stats[i] = gc.get_count()[i]
            
            return MemorySnapshot(
                timestamp=datetime.now(),
                rss_mb=memory_info.rss / 1024 / 1024,  # Convert to MB
                vms_mb=memory_info.vms / 1024 / 1024,
                percent=memory_percent,
                available_mb=system_memory.available / 1024 / 1024,
                gc_collections=gc_stats
            )
        except Exception as e:
            logger.error(f"Failed to get memory usage: {e}")
            return MemorySnapshot(
                timestamp=datetime.now(),
                rss_mb=0, vms_mb=0, percent=0,
                available_mb=0, gc_collections={}
            )
    
    def _record_memory_snapshot(self, snapshot: MemorySnapshot) -> None:
        """Record memory snapshot in history"""
        with self._lock:
            self._memory_history.append(snapshot)
            if len(self._memory_history) > self._max_history:
                self._memory_history.pop(0)
    
    def _should_trigger_gc(self) -> bool:
        """Check if garbage collection should be triggered"""
        now = time.time()
        return (now - self._last_gc_time) >= GARBAGE_COLLECTION_INTERVAL
    
    def _trigger_garbage_collection(self) -> Dict[str, int]:
        """Trigger garbage collection and return stats"""
        self._last_gc_time = time.time()
        
        # Get pre-GC stats
        pre_gc = gc.get_count()
        
        # Force garbage collection for all generations
        collected = {}
        for generation in range(3):
            collected[f"gen_{generation}"] = gc.collect(generation)
        
        # Get post-GC stats
        post_gc = gc.get_count()
        
        logger.debug(f"GC triggered: collected {collected}, counts: {pre_gc} -> {post_gc}")
        return collected
    
    async def _perform_cleanup(self, memory_mb: float) -> bool:
        """Perform memory cleanup operations"""
        if self._cleanup_in_progress:
            return False
        
        self._cleanup_in_progress = True
        cleanup_results = []
        
        try:
            # Trigger garbage collection
            if self._should_trigger_gc():
                gc_stats = self._trigger_garbage_collection()
                cleanup_results.append(f"GC: {gc_stats}")
            
            # Call registered cleanup callbacks
            for callback in self._cleanup_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback()
                    else:
                        callback()
                    cleanup_results.append(f"Callback: {callback.__name__}")
                except Exception as e:
                    logger.error(f"Cleanup callback {callback.__name__} failed: {e}")
            
            # Log cleanup results
            if cleanup_results:
                log_info("Memory cleanup performed", {
                    "memory_before_mb": memory_mb,
                    "cleanup_actions": cleanup_results,
                    "threshold_warning": self._warning_threshold,
                    "threshold_critical": self._critical_threshold
                })
            
            return True
            
        except Exception as e:
            log_error("Memory cleanup failed", e)
            return False
        finally:
            self._cleanup_in_progress = False
    
    async def _check_memory_thresholds(self, snapshot: MemorySnapshot) -> None:
        """Check memory usage against thresholds and take action"""
        memory_mb = snapshot.rss_mb
        now = time.time()
        
        if memory_mb >= self._emergency_threshold:
            # Emergency cleanup
            log_error("EMERGENCY: Memory usage exceeded limit", None, {
                "current_mb": memory_mb,
                "limit_mb": self._emergency_threshold,
                "percent": snapshot.percent
            })
            await self._perform_cleanup(memory_mb)
            
        elif memory_mb >= self._critical_threshold:
            # Critical cleanup
            log_warning("CRITICAL: High memory usage detected", {
                "current_mb": memory_mb,
                "threshold_mb": self._critical_threshold,
                "percent": snapshot.percent
            })
            await self._perform_cleanup(memory_mb)
            
        elif memory_mb >= self._warning_threshold:
            # Warning (rate limited)
            if (now - self._last_warning_time) >= 300:  # 5 minutes
                self._last_warning_time = now
                log_warning("High memory usage detected", {
                    "current_mb": memory_mb,
                    "threshold_mb": self._warning_threshold,
                    "percent": snapshot.percent
                })
    
    async def start_monitoring(self) -> None:
        """Start memory monitoring"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        log_info("Memory monitoring started", {
            "warning_threshold_mb": self._warning_threshold,
            "critical_threshold_mb": self._critical_threshold,
            "emergency_threshold_mb": self._emergency_threshold
        })
    
    async def stop_monitoring(self) -> None:
        """Stop memory monitoring"""
        self._monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        log_info("Memory monitoring stopped")
    
    async def _monitor_loop(self) -> None:
        """Main memory monitoring loop"""
        while self._monitoring:
            try:
                snapshot = self.get_memory_usage()
                self._record_memory_snapshot(snapshot)
                await self._check_memory_thresholds(snapshot)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in memory monitoring loop: {e}")
                await asyncio.sleep(5)
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory statistics"""
        current = self.get_memory_usage()
        
        with self._lock:
            history = self._memory_history.copy()
        
        stats = {
            "current": {
                "rss_mb": current.rss_mb,
                "vms_mb": current.vms_mb,
                "percent": current.percent,
                "available_mb": current.available_mb
            },
            "thresholds": {
                "warning_mb": self._warning_threshold,
                "critical_mb": self._critical_threshold,
                "emergency_mb": self._emergency_threshold
            },
            "cleanup": {
                "callbacks_registered": len(self._cleanup_callbacks),
                "last_gc_time": self._last_gc_time,
                "cleanup_in_progress": self._cleanup_in_progress
            }
        }
        
        if history:
            # Calculate trends
            recent = history[-10:]  # Last 10 snapshots
            if len(recent) > 1:
                avg_rss = sum(s.rss_mb for s in recent) / len(recent)
                max_rss = max(s.rss_mb for s in recent)
                min_rss = min(s.rss_mb for s in recent)
                
                stats["trends"] = {
                    "avg_rss_mb": avg_rss,
                    "max_rss_mb": max_rss,
                    "min_rss_mb": min_rss,
                    "samples": len(recent)
                }
        
        return stats
    
    async def force_cleanup(self) -> bool:
        """Force immediate memory cleanup"""
        current = self.get_memory_usage()
        return await self._perform_cleanup(current.rss_mb)

# Global memory manager instance
memory_manager = MemoryManager()

# Convenience functions
async def start_memory_monitoring():
    """Start memory monitoring"""
    await memory_manager.start_monitoring()

async def stop_memory_monitoring():
    """Stop memory monitoring"""
    await memory_manager.stop_monitoring()

def register_cleanup_callback(callback: Callable):
    """Register a cleanup callback"""
    memory_manager.register_cleanup_callback(callback)

def get_memory_stats() -> Dict[str, Any]:
    """Get memory statistics"""
    return memory_manager.get_memory_stats()

async def force_memory_cleanup() -> bool:
    """Force memory cleanup"""
    return await memory_manager.force_cleanup()
