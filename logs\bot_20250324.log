2025-03-24 19:04:26,600 - discord.client - INFO - logging in using static token
2025-03-24 19:04:29,808 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: c50a2888c59720a134e37e6f6c51ee4a).
2025-03-24 19:07:17,206 - discord.client - INFO - logging in using static token
2025-03-24 19:07:29,785 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 4fb55a79da7d65b8b8a7cfddc73194e5).
2025-03-24 19:33:25,618 - discord.client - INFO - logging in using static token
2025-03-24 19:33:27,461 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 643f3315156015420a20c3d4a58e081e).
2025-03-24 19:39:11,434 - discord.client - INFO - logging in using static token
2025-03-24 19:39:15,032 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 65729fa72edc8ed0d21c433fcb21365c).
2025-03-24 19:42:01,071 - discord.client - INFO - logging in using static token
2025-03-24 19:42:03,090 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 6beed356abffd2f90d134b5cc72ba41c).
2025-03-24 19:43:47,498 - discord.client - INFO - logging in using static token
2025-03-24 19:43:49,228 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 2f2e49ed177a17654342a6b0d461ead7).
2025-03-24 19:46:20,781 - discord.client - INFO - logging in using static token
2025-03-24 19:46:26,031 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: d560f99184c4f73665820d93e7a1e002).
2025-03-24 19:48:11,904 - discord.client - INFO - logging in using static token
2025-03-24 19:48:13,518 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: d6dcb37d495839c53defa1cd883c267f).
2025-03-24 19:49:33,671 - discord.client - INFO - logging in using static token
2025-03-24 19:49:35,220 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 8a8d9a12aa8bc0639e8ccffb0e2ac3ce).
2025-03-24 19:56:36,806 - discord.client - INFO - logging in using static token
2025-03-24 19:56:38,485 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: b45727dce0fff3073a0c53bddcaadc37).
2025-03-24 19:57:46,218 - discord.client - INFO - logging in using static token
2025-03-24 19:57:49,193 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: c9fafe18198f37ed801bb4940332fadf).
2025-03-24 19:59:11,326 - discord.client - INFO - logging in using static token
2025-03-24 19:59:13,153 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: db94e0ad90b7a47af5bbb6d209fee710).
2025-03-24 20:00:59,592 - discord.gateway - INFO - Shard ID None has successfully RESUMED session db94e0ad90b7a47af5bbb6d209fee710.
2025-03-24 20:04:42,879 - discord.gateway - INFO - Shard ID None has successfully RESUMED session db94e0ad90b7a47af5bbb6d209fee710.
2025-03-24 20:09:06,361 - discord.client - INFO - logging in using static token
2025-03-24 20:09:07,838 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 49ca9ec7febeddf3966cb780d893b7af).
2025-03-24 20:11:33,258 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 49ca9ec7febeddf3966cb780d893b7af.
2025-03-24 20:13:47,603 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=ConnectionError('Connection lost')>
aiohttp.client_exceptions.ClientOSError: [WinError 64] The specified network name is no longer available

The above exception was the direct cause of the following exception:

ConnectionError: Connection lost
2025-03-24 20:13:48,358 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 11.9s behind.
2025-03-24 20:13:48,850 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 12.4s behind.
2025-03-24 20:15:55,616 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=ConnectionError('Connection lost')>
aiohttp.client_exceptions.ClientOSError: [WinError 64] The specified network name is no longer available

The above exception was the direct cause of the following exception:

ConnectionError: Connection lost
2025-03-24 20:24:15,947 - discord.client - INFO - logging in using static token
2025-03-24 20:24:17,789 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 1ae97dc546b16270dec9f4e36f8f30ae).
2025-03-24 20:26:08,260 - discord.client - INFO - logging in using static token
2025-03-24 20:26:09,961 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 57fff5e53f4f7716abb256926ebf3672).
2025-03-24 20:27:43,023 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 151, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:27:53,063 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 151, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 341, in run_and_get_output
    with save(image) as (temp_name, input_filename):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 219, in save
    cleanup(f.name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 178, in cleanup
    for filename in iglob(f'{temp_name}*' if temp_name else temp_name):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\glob.py", line 86, in _iglob
    for name in glob_in_dir(_join(root_dir, dirname), basename, dir_fd, dironly):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\glob.py", line 94, in _glob1
    names = _listdir(dirname, dir_fd, dironly)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\glob.py", line 164, in _listdir
    return list(it)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\glob.py", line 147, in _iterdir
    for entry in it:

2025-03-24 20:28:03,086 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 151, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:28:13,096 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 151, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:28:23,113 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 151, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:28:32,194 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 57fff5e53f4f7716abb256926ebf3672.
2025-03-24 20:32:29,620 - discord.client - INFO - logging in using static token
2025-03-24 20:32:31,195 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: ab10f2056b3f063f91ac55f563c6a4f5).
2025-03-24 20:33:22,127 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:33:32,631 - discord.gateway - INFO - Shard ID None has successfully RESUMED session ab10f2056b3f063f91ac55f563c6a4f5.
2025-03-24 20:35:27,599 - discord.client - INFO - logging in using static token
2025-03-24 20:35:29,335 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 690b88ad4663b1af8e4bfd55518e2be7).
2025-03-24 20:37:54,616 - discord.client - INFO - logging in using static token
2025-03-24 20:37:55,791 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: e350f159991711bfd573ba7f59e30bcf).
2025-03-24 20:39:36,535 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:39:46,542 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:39:56,550 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:40:06,559 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:40:16,573 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:40:26,582 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 60 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:40:32,903 - discord.gateway - INFO - Shard ID None has successfully RESUMED session e350f159991711bfd573ba7f59e30bcf.
2025-03-24 20:42:05,274 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:42:13,886 - discord.gateway - INFO - Shard ID None has successfully RESUMED session e350f159991711bfd573ba7f59e30bcf.
2025-03-24 20:47:53,712 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:48:03,731 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:48:13,746 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:48:23,770 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:48:34,333 - discord.gateway - INFO - Shard ID None has successfully RESUMED session e350f159991711bfd573ba7f59e30bcf.
2025-03-24 20:49:25,436 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:49:35,452 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:49:45,479 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:49:49,903 - discord.gateway - INFO - Shard ID None has successfully RESUMED session e350f159991711bfd573ba7f59e30bcf.
2025-03-24 20:52:03,066 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:54:18,265 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:54:26,499 - discord.gateway - INFO - Shard ID None has successfully RESUMED session e350f159991711bfd573ba7f59e30bcf.
2025-03-24 20:55:58,755 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 20:56:06,670 - discord.gateway - INFO - Shard ID None has successfully RESUMED session e350f159991711bfd573ba7f59e30bcf.
2025-03-24 21:03:41,346 - discord.client - INFO - logging in using static token
2025-03-24 21:03:43,403 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: bea16decd84f6e8cbe2534098c70f7fd).
2025-03-24 21:04:34,610 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:04:44,860 - discord.gateway - INFO - Shard ID None has successfully RESUMED session bea16decd84f6e8cbe2534098c70f7fd.
2025-03-24 21:07:30,978 - discord.client - INFO - logging in using static token
2025-03-24 21:07:32,743 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 4a26b7f4f9bd0b801562e528c4353ce3).
2025-03-24 21:14:18,142 - discord.client - INFO - logging in using static token
2025-03-24 21:14:19,777 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 9a64c8f773dbe10406d8288042530cf6).
2025-03-24 21:15:11,727 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 204, in read_amount
    if len(result) >= 6 and result.startswith('0'):

2025-03-24 21:15:21,903 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 211, in read_amount
    processed_results.append('1' + result)

2025-03-24 21:15:32,085 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 204, in read_amount
    if len(result) >= 6 and result.startswith('0'):

2025-03-24 21:15:42,280 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 209, in read_amount
    if len(result) == 5 and result[0] in '123456789':

2025-03-24 21:15:52,519 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 204, in read_amount
    if len(result) >= 6 and result.startswith('0'):

2025-03-24 21:16:02,693 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 60 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 216, in read_amount
    if '19' in result:

2025-03-24 21:16:12,898 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 70 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 211, in read_amount
    processed_results.append('1' + result)

2025-03-24 21:16:23,094 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 80 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 229, in read_amount
    if len(five_digit) == 5 and int(five_digit) < 100000:

2025-03-24 21:16:33,266 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 90 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 204, in read_amount
    if len(result) >= 6 and result.startswith('0'):

2025-03-24 21:16:43,455 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 100 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 229, in read_amount
    if len(five_digit) == 5 and int(five_digit) < 100000:

2025-03-24 21:16:53,918 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 110 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 230, in read_amount
    processed_results.append(five_digit)

2025-03-24 21:17:04,101 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 120 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 211, in read_amount
    processed_results.append('1' + result)

2025-03-24 21:17:14,311 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 130 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 226, in read_amount
    if len(result) == 6 and (result.startswith('1') or result.startswith('7')):

2025-03-24 21:17:50,510 - discord.client - INFO - logging in using static token
2025-03-24 21:17:56,922 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 41bd9c7a3624a3c1fcf2e134f1e73ce9).
2025-03-24 21:18:48,733 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 226, in read_amount
    if len(result) == 6 and (result.startswith('1') or result.startswith('7')):

2025-03-24 21:18:58,914 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 226, in read_amount
    if len(result) == 6 and (result.startswith('1') or result.startswith('7')):

2025-03-24 21:19:09,001 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 204, in read_amount
    if len(result) >= 6 and result.startswith('0'):

2025-03-24 21:19:19,198 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 229, in read_amount
    if len(five_digit) == 5 and int(five_digit) < 100000:

2025-03-24 21:19:29,366 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 221, in read_amount
    if len(result) >= 6 and result[0] == '1' and result[1] in '0123456':

2025-03-24 21:19:39,559 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 60 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 211, in read_amount
    processed_results.append('1' + result)

2025-03-24 21:20:57,667 - discord.client - INFO - logging in using static token
2025-03-24 21:20:59,916 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 0efdec11f9efa7f2baae961b57802018).
2025-03-24 21:24:16,129 - discord.client - INFO - logging in using static token
2025-03-24 21:24:17,728 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: f40c046fd10e5a7172bb61778f29ecb2).
2025-03-24 21:29:10,410 - discord.client - INFO - logging in using static token
2025-03-24 21:29:13,895 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 987d10e81452c4de4db0f88eac25e293).
2025-03-24 21:37:56,100 - discord.client - INFO - logging in using static token
2025-03-24 21:37:58,666 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 47caba985486f6ed9f20a79f979c7104).
2025-03-24 21:38:49,328 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 152, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 341, in run_and_get_output
    with save(image) as (temp_name, input_filename):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 219, in save
    cleanup(f.name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 180, in cleanup
    remove(filename)

2025-03-24 21:46:03,774 - discord.client - INFO - logging in using static token
2025-03-24 21:46:05,579 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 682f351a88bf5767bf63fcae67d05957).
2025-03-24 21:46:56,609 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:47:04,866 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 682f351a88bf5767bf63fcae67d05957.
2025-03-24 21:49:59,655 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:50:08,158 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 682f351a88bf5767bf63fcae67d05957.
2025-03-24 21:51:29,677 - discord.client - INFO - logging in using static token
2025-03-24 21:51:32,152 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 8ab4955c65c8ac5b2befa277268af54e).
2025-03-24 21:52:23,150 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:52:34,164 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 8ab4955c65c8ac5b2befa277268af54e.
2025-03-24 21:53:25,094 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:53:35,108 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:53:45,980 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 8ab4955c65c8ac5b2befa277268af54e.
2025-03-24 21:56:19,893 - discord.client - ERROR - Attempting a reconnect in 0.51s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 375, in from_client
    socket = await client.http.ws_connect(str(url))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\http.py", line 554, in ws_connect
    return await self.__session.ws_connect(url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client.py", line 1004, in _ws_connect
    resp = await self.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client.py", line 703, in _request
    conn = await self._connector.connect(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\connector.py", line 548, in connect
    proto = await self._create_connection(req, traces, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\connector.py", line 1056, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\connector.py", line 1380, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\connector.py", line 1116, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohappyeyeballs\impl.py", line 78, in start_connection
    sock, _, _ = await _staggered.staggered_race(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohappyeyeballs\_staggered.py", line 160, in staggered_race
    done = await _wait_one(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohappyeyeballs\_staggered.py", line 41, in _wait_one
    return await wait_next
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 456, in wait_for
    return fut.result()
asyncio.exceptions.CancelledError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 701, in connect
    self.ws = await asyncio.wait_for(coro, timeout=60.0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 458, in wait_for
    raise exceptions.TimeoutError() from exc
asyncio.exceptions.TimeoutError
2025-03-24 21:56:21,077 - discord.gateway - INFO - Shard ID None session has been invalidated.
2025-03-24 21:56:28,005 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: a8f74c6c42d010907e5b774c60be1bac).
2025-03-24 21:57:13,853 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:57:23,859 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:57:31,836 - discord.gateway - INFO - Shard ID None has successfully RESUMED session a8f74c6c42d010907e5b774c60be1bac.
2025-03-24 21:58:22,859 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:58:32,865 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:58:42,875 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:58:52,890 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:59:02,908 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 21:59:06,691 - discord.gateway - INFO - Shard ID None has successfully RESUMED session a8f74c6c42d010907e5b774c60be1bac.
2025-03-24 22:05:07,665 - discord.client - INFO - logging in using static token
2025-03-24 22:05:08,809 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: a5f9001f7ccb2152612423bf252d5636).
2025-03-24 22:06:41,136 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 22:09:51,106 - discord.client - INFO - logging in using static token
2025-03-24 22:09:53,083 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 8b6a7f2232007d77d70aa20bdb64723c).
2025-03-24 22:10:44,122 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 275, in run_tesseract
    proc = subprocess.Popen(cmd_args, **subprocess_args())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 971, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1440, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,

2025-03-24 22:15:00,428 - discord.client - INFO - logging in using static token
2025-03-24 22:15:02,406 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 1fe70830d0c9906b74d720945772ddc4).
2025-03-24 22:15:53,413 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 22:16:03,417 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 22:16:12,084 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 1fe70830d0c9906b74d720945772ddc4.
2025-03-24 22:17:03,158 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 22:17:10,670 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 1fe70830d0c9906b74d720945772ddc4.
2025-03-24 22:20:28,091 - discord.client - INFO - logging in using static token
2025-03-24 22:20:30,051 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 879c05585198adf9e8a8506cc153c8a6).
2025-03-24 22:21:21,013 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 22:21:30,839 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 879c05585198adf9e8a8506cc153c8a6.
2025-03-24 22:22:19,630 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 22:22:29,638 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 22:22:39,657 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 22:22:49,279 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 879c05585198adf9e8a8506cc153c8a6.
2025-03-24 22:25:46,672 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 13.1s behind.
2025-03-24 22:27:17,591 - discord.client - INFO - logging in using static token
2025-03-24 22:27:21,807 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: be40a25a57b49beffee6be574efbed4b).
2025-03-24 22:28:12,415 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 22:28:22,422 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 22:29:16,230 - discord.gateway - INFO - Shard ID None has successfully RESUMED session be40a25a57b49beffee6be574efbed4b.
2025-03-24 22:43:59,157 - discord.client - INFO - logging in using static token
2025-03-24 22:44:01,718 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: ab97ea80e4aa1d79849dc00a568e4efe).
2025-03-24 22:44:52,659 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 22:45:02,675 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 165, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1173, in communicate
    sts = self.wait(timeout=self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1209, in wait
    return self._wait(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1490, in _wait
    result = _winapi.WaitForSingleObject(self._handle,

2025-03-24 22:45:13,954 - discord.gateway - INFO - Shard ID None has successfully RESUMED session ab97ea80e4aa1d79849dc00a568e4efe.
2025-03-24 22:51:32,338 - discord.client - INFO - logging in using static token
2025-03-24 22:51:33,914 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: cc9263ea6cc03b4571eb52d83fbf50dd).
2025-03-24 22:57:11,714 - discord.client - INFO - logging in using static token
2025-03-24 22:57:13,253 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: b936b86ef8d61cdbe0deccef4714ee92).
2025-03-24 22:59:07,217 - discord.client - INFO - logging in using static token
2025-03-24 22:59:09,331 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: f4067850669934a807bf91032d2c48ea).
2025-03-24 23:00:29,796 - discord.client - INFO - logging in using static token
2025-03-24 23:00:31,783 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: e84921d177d6cb6d531e4d872ed536fc).
2025-03-24 23:01:22,656 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:01:32,661 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:01:33,579 - discord.gateway - INFO - Shard ID None has successfully RESUMED session e84921d177d6cb6d531e4d872ed536fc.
2025-03-24 23:02:24,593 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:02:34,609 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:02:43,444 - discord.gateway - INFO - Shard ID None has successfully RESUMED session e84921d177d6cb6d531e4d872ed536fc.
2025-03-24 23:07:53,859 - discord.client - INFO - logging in using static token
2025-03-24 23:07:55,266 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 8cf67c3fdec3207281e9e272b640037e).
2025-03-24 23:09:28,782 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:09:38,796 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:09:46,966 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 8cf67c3fdec3207281e9e272b640037e.
2025-03-24 23:10:37,272 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:10:47,276 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:10:57,284 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:11:08,456 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 8cf67c3fdec3207281e9e272b640037e.
2025-03-24 23:11:59,157 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:12:09,175 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:12:16,681 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 8cf67c3fdec3207281e9e272b640037e.
2025-03-24 23:12:43,162 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=ConnectionError('Connection lost')>
aiohttp.client_exceptions.ClientOSError: [WinError 64] The specified network name is no longer available

The above exception was the direct cause of the following exception:

ConnectionError: Connection lost
2025-03-24 23:13:54,257 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 20.5s behind.
2025-03-24 23:13:54,808 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 21.1s behind.
2025-03-24 23:18:38,043 - discord.client - INFO - logging in using static token
2025-03-24 23:18:39,868 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: c18f3c6344c0dcbc0671645c658ddf94).
2025-03-24 23:20:19,252 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:20:34,885 - discord.gateway - INFO - Shard ID None has successfully RESUMED session c18f3c6344c0dcbc0671645c658ddf94.
2025-03-24 23:22:45,770 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:22:55,787 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:22:58,620 - discord.gateway - INFO - Shard ID None has successfully RESUMED session c18f3c6344c0dcbc0671645c658ddf94.
2025-03-24 23:23:49,678 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:23:59,695 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:24:09,716 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:24:10,794 - discord.gateway - INFO - Shard ID None has successfully RESUMED session c18f3c6344c0dcbc0671645c658ddf94.
2025-03-24 23:26:24,359 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:27:20,748 - discord.client - INFO - logging in using static token
2025-03-24 23:27:22,993 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 57e9f087c5f2fe3fd426f1911ed31042).
2025-03-24 23:28:13,862 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:28:23,876 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:28:28,927 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 57e9f087c5f2fe3fd426f1911ed31042.
2025-03-24 23:31:55,761 - discord.client - INFO - logging in using static token
2025-03-24 23:31:57,232 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 353a47d5ee1dd4df066297cf6b055c84).
2025-03-24 23:32:48,259 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:32:58,271 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:33:00,539 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 353a47d5ee1dd4df066297cf6b055c84.
2025-03-24 23:36:31,563 - discord.client - INFO - logging in using static token
2025-03-24 23:36:32,892 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: ec15703a88ef9a7af7b1a719e230014d).
2025-03-24 23:37:23,945 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:37:33,956 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:37:43,967 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:37:53,979 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:38:03,987 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 486, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 489, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 352, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 282, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 144, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:38:05,908 - discord.gateway - INFO - Shard ID None has successfully RESUMED session ec15703a88ef9a7af7b1a719e230014d.
2025-03-24 14:52:50,414 - discord.client - INFO - logging in using static token
2025-03-24 14:52:51,079 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: fda93121875c46c260bebe8676ab6a9d).
2025-03-24 14:55:57,621 - discord.client - INFO - logging in using static token
2025-03-24 14:55:58,213 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 3079e3a5c6e0172e97e372396090e08c).
2025-03-24 15:05:27,323 - discord.client - INFO - logging in using static token
2025-03-24 15:05:28,055 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: f08b354cb393915f72b96e08a5f3afd4).
2025-03-24 15:06:19,814 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 15:06:29,863 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 15:06:39,868 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 15:06:49,879 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 15:06:59,921 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 15:07:02,682 - discord.gateway - INFO - Shard ID None has successfully RESUMED session f08b354cb393915f72b96e08a5f3afd4.
2025-03-24 17:07:01,997 - discord.gateway - INFO - Shard ID None has successfully RESUMED session f08b354cb393915f72b96e08a5f3afd4.
2025-03-24 18:30:45,029 - discord.gateway - INFO - Shard ID None has successfully RESUMED session f08b354cb393915f72b96e08a5f3afd4.
2025-03-24 21:29:09,169 - discord.gateway - INFO - Shard ID None has successfully RESUMED session f08b354cb393915f72b96e08a5f3afd4.
2025-03-24 23:25:32,789 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:25:42,809 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-24 23:25:46,057 - discord.client - ERROR - Attempting a reconnect in 1.21s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-03-24 23:25:47,434 - discord.gateway - INFO - Shard ID None has successfully RESUMED session f08b354cb393915f72b96e08a5f3afd4.
2025-03-25 03:15:14,329 - discord.gateway - INFO - Shard ID None has successfully RESUMED session f08b354cb393915f72b96e08a5f3afd4.
2025-03-25 05:29:06,504 - discord.gateway - INFO - Shard ID None has successfully RESUMED session f08b354cb393915f72b96e08a5f3afd4.
2025-03-25 06:49:03,121 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 324, in _loop_reading
    self._data_received(data, length)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 274, in _data_received
    self._protocol.data_received(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\sslproto.py", line 551, in data_received
    self._app_protocol.data_received(chunk)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client_proto.py", line 198, in data_received
    eof, tail = self._payload_parser.feed_data(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\http_websocket.py", line 288, in feed_data
    return self._feed_data(data)

2025-03-25 06:49:20,765 - discord.client - ERROR - Attempting a reconnect in 1.54s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 656, in connect
    self.ws = await asyncio.wait_for(coro, timeout=60.0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 445, in wait_for
    return fut.result()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 372, in from_client
    socket = await client.http.ws_connect(str(url))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\http.py", line 537, in ws_connect
    return await self.__session.ws_connect(url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client.py", line 779, in _ws_connect
    resp = await self.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client.py", line 560, in _request
    await resp.start(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client_reqrep.py", line 899, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\streams.py", line 616, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [WinError 10053] An established connection was aborted by the software in your host machine
2025-03-25 06:54:06,157 - discord.client - ERROR - Attempting a reconnect in 1.94s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 656, in connect
    self.ws = await asyncio.wait_for(coro, timeout=60.0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 445, in wait_for
    return fut.result()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 372, in from_client
    socket = await client.http.ws_connect(str(url))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\http.py", line 537, in ws_connect
    return await self.__session.ws_connect(url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client.py", line 779, in _ws_connect
    resp = await self.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client.py", line 560, in _request
    await resp.start(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client_reqrep.py", line 899, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\streams.py", line 616, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [WinError 10053] An established connection was aborted by the software in your host machine
2025-03-25 06:55:05,034 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 15.7s behind.
2025-03-25 06:55:09,622 - discord.gateway - INFO - Shard ID None session has been invalidated.
2025-03-25 06:55:53,145 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 14.8s behind.
2025-03-25 06:55:55,243 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 056ead13a0f315bdd4606afb79c35299).
2025-03-25 06:58:01,228 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 60, in on_ready
    info = sheet.spreadsheet.sheet1.get('A1')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\gspread\spreadsheet.py", line 110, in sheet1
    return self.get_worksheet(0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\gspread\spreadsheet.py", line 274, in get_worksheet
    sheet_data = self.fetch_sheet_metadata()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\gspread\spreadsheet.py", line 254, in fetch_sheet_metadata
    r = self.client.request("get", url, params=params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\gspread\client.py", line 79, in request
    response = getattr(self.session, method)(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\google\auth\transport\requests.py", line 533, in request
    self.credentials.before_request(auth_request, method, url, request_headers)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\google\auth\credentials.py", line 239, in before_request
    self._blocking_refresh(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\google\auth\credentials.py", line 202, in _blocking_refresh
    self.refresh(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\google\oauth2\service_account.py", line 448, in refresh
    access_token, expiry, _ = _client.jwt_grant(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\google\oauth2\_client.py", line 299, in jwt_grant
    response_data = _token_endpoint_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\google\oauth2\_client.py", line 259, in _token_endpoint_request
    response_status_ok, response_data, retryable_error = _token_endpoint_request_no_throw(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\google\oauth2\_client.py", line 192, in _token_endpoint_request_no_throw
    response = request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\google\auth\transport\requests.py", line 186, in __call__
    response = self.session.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\requests\adapters.py", line 640, in send
    url = self.request_url(request, proxies)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\requests\adapters.py", line 342, in cert_verify
    if cert:

2025-03-25 06:59:27,771 - discord.client - ERROR - Attempting a reconnect in 6.09s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 372, in from_client
    socket = await client.http.ws_connect(str(url))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\http.py", line 537, in ws_connect
    return await self.__session.ws_connect(url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client.py", line 779, in _ws_connect
    resp = await self.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client.py", line 560, in _request
    await resp.start(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\client_reqrep.py", line 899, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\aiohttp\streams.py", line 616, in read
    await self._waiter
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 456, in wait_for
    return fut.result()
asyncio.exceptions.CancelledError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 656, in connect
    self.ws = await asyncio.wait_for(coro, timeout=60.0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 458, in wait_for
    raise exceptions.TimeoutError() from exc
asyncio.exceptions.TimeoutError
2025-03-25 07:00:23,132 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 11.0s behind.
2025-03-25 07:00:26,541 - discord.gateway - INFO - Shard ID None session has been invalidated.
2025-03-25 07:01:06,963 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 24.8s behind.
2025-03-25 07:01:08,596 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 7a5b8c746c6746ab49faba5ce8f2c1d9).
