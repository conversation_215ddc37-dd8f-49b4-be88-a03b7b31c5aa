"""
Status Dashboard for Hall of Heroes Bot
Provides comprehensive system monitoring and statistics
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
import logging

from webhook_logger import log_info
from performance_monitor import get_performance_stats
from memory_manager import get_memory_stats
from resource_pool import get_resource_stats
from connection_monitor import get_health_summary
from concurrent_processor import get_processing_stats
from model_manager import get_model_stats

logger = logging.getLogger(__name__)

@dataclass
class SystemStatus:
    """System status snapshot"""
    timestamp: datetime
    uptime_seconds: float
    overall_health: str
    memory_usage_mb: float
    active_connections: int
    processing_queue_size: int
    total_processed: int
    error_count: int
    performance_score: float

class StatusDashboard:
    """
    Comprehensive system status dashboard
    """
    
    def __init__(self):
        self.start_time = time.time()
        self.status_history: List[SystemStatus] = []
        self.max_history = 1000
        self.last_report_time = 0
        self.report_interval = 300  # 5 minutes
        
        # Counters
        self.total_errors = 0
        self.total_warnings = 0
        self.total_processed_images = 0
        
    def get_uptime(self) -> float:
        """Get system uptime in seconds"""
        return time.time() - self.start_time
    
    def calculate_performance_score(self, stats: Dict[str, Any]) -> float:
        """
        Calculate overall performance score (0-100)
        """
        score = 100.0
        
        # Memory usage penalty
        memory_stats = stats.get('memory', {})
        current_memory = memory_stats.get('current', {}).get('rss_mb', 0)
        memory_threshold = memory_stats.get('thresholds', {}).get('warning_mb', 1000)
        
        if current_memory > memory_threshold:
            memory_penalty = min(30, (current_memory / memory_threshold - 1) * 50)
            score -= memory_penalty
        
        # Connection health penalty
        health_summary = stats.get('health', {})
        if not health_summary.get('overall_healthy', True):
            score -= 20
        
        # Processing performance bonus/penalty
        processing_stats = stats.get('processing', {})
        success_rate = processing_stats.get('performance', {}).get('success_rate', 1.0)
        if success_rate < 0.9:
            score -= (1.0 - success_rate) * 30
        elif success_rate > 0.95:
            score += 5  # Bonus for high success rate
        
        # Performance timing penalty
        perf_stats = stats.get('performance', {})
        if 'by_operation' in perf_stats:
            avg_times = [op.get('avg_duration', 0) for op in perf_stats['by_operation'].values()]
            if avg_times and max(avg_times) > 10:  # More than 10 seconds
                score -= 10
        
        return max(0, min(100, score))
    
    def get_comprehensive_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            # Collect all statistics
            stats = {
                'timestamp': datetime.now().isoformat(),
                'uptime_seconds': self.get_uptime(),
                'memory': get_memory_stats(),
                'performance': get_performance_stats(),
                'resources': get_resource_stats(),
                'health': get_health_summary(),
                'processing': get_processing_stats(),
                'model': get_model_stats()
            }
            
            # Calculate derived metrics
            memory_current = stats['memory'].get('current', {}).get('rss_mb', 0)
            processing_stats = stats['processing']
            
            # Overall health assessment
            health_issues = []
            overall_health = "healthy"
            
            # Check memory
            memory_thresholds = stats['memory'].get('thresholds', {})
            if memory_current > memory_thresholds.get('critical_mb', 900):
                health_issues.append("high_memory")
                overall_health = "critical"
            elif memory_current > memory_thresholds.get('warning_mb', 800):
                health_issues.append("elevated_memory")
                if overall_health == "healthy":
                    overall_health = "warning"
            
            # Check connections
            if not stats['health'].get('overall_healthy', True):
                health_issues.append("connection_issues")
                overall_health = "critical"
            
            # Check processing performance
            success_rate = processing_stats.get('performance', {}).get('success_rate', 1.0)
            if success_rate < 0.8:
                health_issues.append("low_success_rate")
                if overall_health in ["healthy", "warning"]:
                    overall_health = "critical"
            elif success_rate < 0.9:
                health_issues.append("reduced_success_rate")
                if overall_health == "healthy":
                    overall_health = "warning"
            
            # Add summary information
            stats['summary'] = {
                'overall_health': overall_health,
                'health_issues': health_issues,
                'performance_score': self.calculate_performance_score(stats),
                'active_jobs': processing_stats.get('jobs', {}).get('active', 0),
                'queue_size': processing_stats.get('queue', {}).get('queue_size', 0),
                'total_processed': processing_stats.get('jobs', {}).get('total_processed', 0),
                'memory_usage_mb': memory_current,
                'uptime_hours': self.get_uptime() / 3600
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get comprehensive status: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'summary': {
                    'overall_health': 'error',
                    'health_issues': ['status_collection_failed'],
                    'performance_score': 0
                }
            }
    
    def create_status_snapshot(self) -> SystemStatus:
        """Create a status snapshot"""
        stats = self.get_comprehensive_status()
        summary = stats.get('summary', {})
        
        return SystemStatus(
            timestamp=datetime.now(),
            uptime_seconds=self.get_uptime(),
            overall_health=summary.get('overall_health', 'unknown'),
            memory_usage_mb=summary.get('memory_usage_mb', 0),
            active_connections=len(stats.get('health', {}).get('services', {})),
            processing_queue_size=summary.get('queue_size', 0),
            total_processed=summary.get('total_processed', 0),
            error_count=self.total_errors,
            performance_score=summary.get('performance_score', 0)
        )
    
    def record_status_snapshot(self):
        """Record a status snapshot in history"""
        snapshot = self.create_status_snapshot()
        
        self.status_history.append(snapshot)
        
        # Limit history size
        if len(self.status_history) > self.max_history:
            self.status_history.pop(0)
    
    def get_status_trends(self, hours: int = 24) -> Dict[str, Any]:
        """Get status trends over specified time period"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_snapshots = [
            s for s in self.status_history 
            if s.timestamp >= cutoff_time
        ]
        
        if not recent_snapshots:
            return {"error": "No data available for specified time period"}
        
        # Calculate trends
        memory_values = [s.memory_usage_mb for s in recent_snapshots]
        performance_values = [s.performance_score for s in recent_snapshots]
        
        trends = {
            "time_period_hours": hours,
            "snapshots_count": len(recent_snapshots),
            "memory_trend": {
                "current": memory_values[-1] if memory_values else 0,
                "average": sum(memory_values) / len(memory_values) if memory_values else 0,
                "min": min(memory_values) if memory_values else 0,
                "max": max(memory_values) if memory_values else 0,
                "trend": "increasing" if len(memory_values) > 1 and memory_values[-1] > memory_values[0] else "stable"
            },
            "performance_trend": {
                "current": performance_values[-1] if performance_values else 0,
                "average": sum(performance_values) / len(performance_values) if performance_values else 0,
                "min": min(performance_values) if performance_values else 0,
                "max": max(performance_values) if performance_values else 0
            },
            "health_distribution": {
                "healthy": sum(1 for s in recent_snapshots if s.overall_health == "healthy"),
                "warning": sum(1 for s in recent_snapshots if s.overall_health == "warning"),
                "critical": sum(1 for s in recent_snapshots if s.overall_health == "critical"),
                "error": sum(1 for s in recent_snapshots if s.overall_health == "error")
            }
        }
        
        return trends
    
    def should_send_status_report(self) -> bool:
        """Check if it's time to send a status report"""
        return (time.time() - self.last_report_time) >= self.report_interval
    
    async def send_status_report(self):
        """Send periodic status report via webhook"""
        if not self.should_send_status_report():
            return
        
        try:
            status = self.get_comprehensive_status()
            summary = status.get('summary', {})
            
            # Create status message
            health_emoji = {
                'healthy': '✅',
                'warning': '⚠️',
                'critical': '🚨',
                'error': '❌'
            }

            overall_health = summary.get('overall_health', 'unknown')
            emoji = health_emoji.get(overall_health, '❓')

            message = f"{emoji} **System Status Report**\n"
            message += f"Health: {overall_health.title()}\n"
            message += f"Uptime: {summary.get('uptime_hours', 0):.1f} hours\n"
            message += f"Performance Score: {summary.get('performance_score', 0):.1f}/100\n"
            message += f"Memory Usage: {summary.get('memory_usage_mb', 0):.1f} MB\n"
            message += f"Active Jobs: {summary.get('active_jobs', 0)}\n"
            message += f"Total Processed: {summary.get('total_processed', 0)}"
            
            # Add health issues if any
            health_issues = summary.get('health_issues', [])
            if health_issues:
                message += f"\n\n⚠️ Issues: {', '.join(health_issues)}"
            
            log_info(message, {
                "report_type": "periodic_status",
                "overall_health": overall_health,
                "performance_score": summary.get('performance_score', 0),
                "uptime_hours": summary.get('uptime_hours', 0)
            })
            
            self.last_report_time = time.time()
            
        except Exception as e:
            logger.error(f"Failed to send status report: {e}")
    
    def increment_error_count(self):
        """Increment error counter"""
        self.total_errors += 1
    
    def increment_warning_count(self):
        """Increment warning counter"""
        self.total_warnings += 1
    
    def increment_processed_count(self):
        """Increment processed images counter"""
        self.total_processed_images += 1
    
    def get_quick_status(self) -> str:
        """Get quick status string for console output"""
        try:
            status = self.get_comprehensive_status()
            summary = status.get('summary', {})
            
            health = summary.get('overall_health', 'unknown')
            uptime = summary.get('uptime_hours', 0)
            memory = summary.get('memory_usage_mb', 0)
            score = summary.get('performance_score', 0)
            
            return f"Health: {health} | Uptime: {uptime:.1f}h | Memory: {memory:.0f}MB | Score: {score:.0f}/100"
            
        except Exception as e:
            return f"Status Error: {str(e)}"

# Global status dashboard
status_dashboard = StatusDashboard()

# Convenience functions
def get_system_status() -> Dict[str, Any]:
    """Get comprehensive system status"""
    return status_dashboard.get_comprehensive_status()

def get_status_trends(hours: int = 24) -> Dict[str, Any]:
    """Get status trends"""
    return status_dashboard.get_status_trends(hours)

def record_status():
    """Record status snapshot"""
    status_dashboard.record_status_snapshot()

async def send_periodic_status_report():
    """Send periodic status report"""
    await status_dashboard.send_status_report()

def get_quick_status() -> str:
    """Get quick status string"""
    return status_dashboard.get_quick_status()

def increment_error_count():
    """Increment error counter"""
    status_dashboard.increment_error_count()

def increment_processed_count():
    """Increment processed counter"""
    status_dashboard.increment_processed_count()

# Status monitoring task
async def start_status_monitoring():
    """Start periodic status monitoring"""
    while True:
        try:
            # Record status snapshot
            record_status()
            
            # Send periodic report
            await send_periodic_status_report()
            
            # Wait before next check
            await asyncio.sleep(60)  # Check every minute
            
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"Error in status monitoring: {e}")
            await asyncio.sleep(5)
