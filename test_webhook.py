"""
Test script for webhook logging system
"""

import asyncio
import os
from webhook_logger import log_info, log_error, log_warning, log_critical, webhook_logger

async def test_webhook_logging():
    """Test the webhook logging system"""
    
    print("=== Testing Webhook Logging System ===")
    
    # Check if webhook URLs are configured
    error_url = os.getenv('ERROR_WEBHOOK_URL')
    status_url = os.getenv('STATUS_WEBHOOK_URL')
    review_url = os.getenv('REVIEW_WEBHOOK_URL')
    
    print(f"Error webhook URL configured: {'✅' if error_url else '❌'}")
    print(f"Status webhook URL configured: {'✅' if status_url else '❌'}")
    print(f"Review webhook URL configured: {'✅' if review_url else '❌'}")
    
    if not any([error_url, status_url, review_url]):
        print("\n⚠️  No webhook URLs configured. Please set environment variables:")
        print("   - ERROR_WEBHOOK_URL")
        print("   - STATUS_WEBHOOK_URL") 
        print("   - REVIEW_WEBHOOK_URL")
        print("\nSkipping webhook tests...")
        return
    
    print("\n🧪 Testing webhook notifications...")
    
    # Test info message
    log_info("Test info message from Hall of Heroes Bot", {
        "test_type": "webhook_test",
        "timestamp": "2025-01-01 12:00:00",
        "status": "testing"
    })
    
    # Test warning message
    log_warning("Test warning message", {
        "test_type": "webhook_test",
        "warning_level": "medium"
    })
    
    # Test error message
    try:
        raise ValueError("This is a test error")
    except Exception as e:
        log_error("Test error message", e, {
            "test_type": "webhook_test",
            "error_context": "testing_error_handling"
        })
    
    # Test critical message
    log_critical("Test critical message", None, {
        "test_type": "webhook_test",
        "severity": "high",
        "requires_attention": True
    })
    
    # Test review request
    await webhook_logger.send_review_request(
        {
            "message_id": "test_123",
            "user": "test_user",
            "filename": "test_image.png"
        },
        {
            "tier_confidence": 0.45,
            "type_confidence": 0.60,
            "amount_confidence": 0.30
        }
    )
    
    print("✅ Webhook test messages sent!")
    print("📱 Check your Discord channels for the test notifications.")
    print("\nIf you don't see messages, verify:")
    print("   1. Webhook URLs are correct")
    print("   2. Bot has permission to send messages")
    print("   3. Webhooks are not rate limited")

if __name__ == "__main__":
    asyncio.run(test_webhook_logging())
