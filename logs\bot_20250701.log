2025-07-01 12:19:11,387 - discord.client - INFO - logging in using static token
2025-07-01 12:19:12,012 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 4154c9037a10fd43dbc7d5fe6b043ccc).
2025-07-01 12:20:44,683 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 12:20:54,699 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 12:21:02,558 - discord.client - ERROR - Attempting a reconnect in 1.61s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-01 12:21:04,324 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 12:25:21,884 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 12:25:31,900 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 12:25:41,915 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 12:25:46,275 - discord.client - ERROR - Attempting a reconnect in 3.83s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-01 12:25:50,228 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 12:30:07,804 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 12:30:17,820 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 12:30:27,835 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 12:30:37,851 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 12:30:40,976 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 12:31:32,194 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 12:31:42,210 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 12:31:52,319 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 136, in on_message
    processed_image.save(temp_image_path)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PIL\Image.py", line 2413, in save
    save_handler(self, fp, filename)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PIL\PngImagePlugin.py", line 1398, in _save
    ImageFile._save(im, _idat(fp, chunk), [("zip", (0, 0) + im.size, 0, rawmode)])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PIL\ImageFile.py", line 519, in _save
    _encode_tile(im, fp, tile, bufsize, None, exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PIL\ImageFile.py", line 538, in _encode_tile
    errcode, data = encoder.encode(bufsize)[1:]

2025-07-01 12:31:52,756 - discord.client - ERROR - Attempting a reconnect in 6.27s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-01 12:31:59,178 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 13:19:24,204 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 13:51:12,378 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 13:51:22,394 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 13:51:26,316 - discord.client - ERROR - Attempting a reconnect in 1.15s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-01 13:51:28,534 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 13:57:08,610 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 13:57:18,625 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 13:57:21,516 - discord.client - ERROR - Attempting a reconnect in 0.14s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-01 13:57:21,797 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 13:58:13,031 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 13:58:23,047 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 13:58:33,062 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 13:58:43,078 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 13:58:53,094 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 13:59:03,109 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 60 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 13:59:06,312 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 14:03:23,857 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 14:03:33,873 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 14:03:43,888 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 14:03:53,904 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 14:04:03,919 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 14:04:06,685 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 14:09:46,761 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 14:09:56,776 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 14:10:03,870 - discord.client - ERROR - Attempting a reconnect in 4.05s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-01 14:10:08,058 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 14:41:14,950 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 14:41:24,966 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 14:41:34,981 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 14:41:39,325 - discord.client - ERROR - Attempting a reconnect in 4.20s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-01 14:41:43,654 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 14:42:34,887 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 14:42:41,934 - discord.client - ERROR - Attempting a reconnect in 14.77s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-01 14:42:56,840 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 16:01:31,057 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 16:43:46,742 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 16:50:49,349 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 16:50:59,364 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 16:51:09,380 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 16:51:13,114 - discord.client - ERROR - Attempting a reconnect in 1.91s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-01 16:51:15,193 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 16:56:14,018 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 16:56:24,034 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-01 16:56:29,768 - discord.client - ERROR - Attempting a reconnect in 0.33s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-01 16:56:30,253 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 17:42:13,760 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 18:14:00,378 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 20:12:26,109 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-01 23:26:04,162 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-02 00:14:22,688 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 00:14:32,704 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 00:14:42,719 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 00:14:52,735 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 00:14:55,329 - discord.client - ERROR - Attempting a reconnect in 0.14s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-02 00:14:55,579 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-02 00:16:28,062 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 00:16:38,078 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 00:16:43,359 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 4154c9037a10fd43dbc7d5fe6b043ccc.
2025-07-02 00:18:57,139 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 00:19:03,702 - discord.client - ERROR - Attempting a reconnect in 3.43s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-02 00:20:27,701 - discord.gateway - INFO - Shard ID None session has been invalidated.
2025-07-02 00:20:32,935 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: cc303b9438c2c45fad9560adc2d7522c).
2025-07-02 00:22:00,341 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 37, in extract_deads
    print(f"Tiers dictionary: {tiers}")

