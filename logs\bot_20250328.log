2025-03-28 14:10:02,715 - discord.client - INFO - logging in using static token
2025-03-28 14:10:03,465 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 2bb273ff74dee7fceaabcb743c3e2b40).
2025-03-28 15:19:24,215 - discord.client - INFO - logging in using static token
2025-03-28 15:19:24,806 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 325a36f3047d6d65a05f0e2b328b6077).
2025-03-28 15:29:12,543 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-28 15:29:22,549 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-28 15:29:24,331 - discord.client - ERROR - Attempting a reconnect in 0.30s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-03-28 15:29:24,890 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 325a36f3047d6d65a05f0e2b328b6077.
2025-03-28 19:08:03,943 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 325a36f3047d6d65a05f0e2b328b6077.
2025-03-28 19:26:47,885 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 277, in run_and_get_output
    with save(image) as (temp_name, input_filename):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 193, in save
    with NamedTemporaryFile(prefix='tess_', delete=False) as f:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\tempfile.py", line 559, in NamedTemporaryFile
    file = _io.open(dir, mode, buffering=buffering,

2025-03-28 19:26:57,894 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-28 19:27:07,900 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-28 19:27:17,906 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-28 19:27:23,751 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 325a36f3047d6d65a05f0e2b328b6077.
2025-03-28 22:57:58,573 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-03-28 22:58:08,582 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 255, in run_tesseract
    proc = subprocess.Popen(cmd_args, **subprocess_args())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 971, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1440, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,

2025-03-28 22:58:15,307 - discord.client - ERROR - Attempting a reconnect in 0.54s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-03-28 22:58:16,002 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 325a36f3047d6d65a05f0e2b328b6077.
2025-03-28 23:31:41,721 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 325a36f3047d6d65a05f0e2b328b6077.
2025-03-29 01:21:43,160 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 325a36f3047d6d65a05f0e2b328b6077.
2025-03-29 02:18:27,375 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 325a36f3047d6d65a05f0e2b328b6077.
2025-03-29 03:36:52,779 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 325a36f3047d6d65a05f0e2b328b6077.
