"""
Comprehensive Testing Framework for Hall of Heroes Bot
Provides unit tests, integration tests, and performance benchmarks
"""

import unittest
import asyncio
import time
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any, List
import numpy as np
from PIL import Image

class TestConfig(unittest.TestCase):
    """Test configuration system"""
    
    def test_config_imports(self):
        """Test that all config imports work"""
        try:
            from config import (
                TIER_MAPPING, TYPE_MAPPING, SPREADSHEET_TYPE_ORDER,
                get_spreadsheet_headers, YOLO_MODEL_PATH
            )
            self.assertIsInstance(TIER_MAPPING, dict)
            self.assertIsInstance(TYPE_MAPPING, dict)
            self.assertIsInstance(SPREADSHEET_TYPE_ORDER, list)
            self.assertTrue(callable(get_spreadsheet_headers))
        except ImportError as e:
            self.fail(f"Config import failed: {e}")
    
    def test_spreadsheet_headers(self):
        """Test spreadsheet header generation"""
        from config import get_spreadsheet_headers
        headers = get_spreadsheet_headers()
        
        self.assertIsInstance(headers, list)
        self.assertGreater(len(headers), 0)
        self.assertEqual(headers[0], 'Player ID')
        
        # Should have headers for all tier/type combinations
        expected_count = 1 + (5 * 4)  # Player ID + (5 tiers * 4 types)
        self.assertEqual(len(headers), expected_count)

class TestModelManager(unittest.TestCase):
    """Test model manager functionality"""
    
    def test_model_manager_singleton(self):
        """Test that model manager is a singleton"""
        from model_manager import ModelManager
        
        manager1 = ModelManager()
        manager2 = ModelManager()
        
        self.assertIs(manager1, manager2)
    
    def test_model_stats(self):
        """Test model statistics"""
        from model_manager import get_model_stats
        
        stats = get_model_stats()
        self.assertIsInstance(stats, dict)
        self.assertIn('model_loaded', stats)
        self.assertIn('load_count', stats)

class TestPerformanceMonitor(unittest.TestCase):
    """Test performance monitoring system"""
    
    def test_performance_timer(self):
        """Test performance timer context manager"""
        from performance_monitor import PerformanceTimer
        
        with PerformanceTimer("test_operation") as timer:
            time.sleep(0.1)
        
        # Timer should have recorded the operation
        self.assertIsNotNone(timer)
    
    def test_performance_stats(self):
        """Test performance statistics"""
        from performance_monitor import get_performance_stats
        
        stats = get_performance_stats()
        self.assertIsInstance(stats, dict)

class TestWebhookLogger(unittest.TestCase):
    """Test webhook logging system"""
    
    def test_webhook_logger_functions(self):
        """Test webhook logger convenience functions"""
        from webhook_logger import log_info, log_warning, log_error
        
        # These should not raise exceptions
        log_info("Test info message", {"test": True})
        log_warning("Test warning message")
        log_error("Test error message", None, {"context": "test"})

class TestMemoryManager(unittest.TestCase):
    """Test memory management system"""
    
    def test_memory_stats(self):
        """Test memory statistics"""
        from memory_manager import get_memory_stats
        
        stats = get_memory_stats()
        self.assertIsInstance(stats, dict)
        self.assertIn('current', stats)
        self.assertIn('thresholds', stats)

class TestResourcePool(unittest.TestCase):
    """Test resource pooling system"""
    
    def test_resource_pool_creation(self):
        """Test resource pool creation"""
        from resource_pool import create_resource_pool
        
        def dummy_factory():
            return "test_resource"
        
        pool = create_resource_pool("test_pool", dummy_factory, max_size=5)
        self.assertIsNotNone(pool)
    
    def test_image_cache(self):
        """Test image cache functionality"""
        from resource_pool import get_image_cache
        
        cache = get_image_cache()
        
        # Test cache operations
        cache.put("test_key", "test_value")
        value = cache.get("test_key")
        self.assertEqual(value, "test_value")
        
        # Test cache miss
        missing = cache.get("nonexistent_key")
        self.assertIsNone(missing)

class TestEnhancedOCR(unittest.TestCase):
    """Test enhanced OCR system"""
    
    def test_image_preprocessor(self):
        """Test image preprocessing"""
        from enhanced_ocr import ImagePreprocessor
        
        preprocessor = ImagePreprocessor()
        
        # Create a test image
        test_image = np.zeros((100, 100), dtype=np.uint8)
        
        variants = preprocessor.preprocess_variants(test_image)
        self.assertIsInstance(variants, dict)
        self.assertGreater(len(variants), 0)
        self.assertIn('original', variants)
    
    def test_ocr_result_validation(self):
        """Test OCR result validation"""
        from enhanced_ocr import validate_ocr_result
        
        # Valid results
        self.assertTrue(validate_ocr_result("12345", 0.8))
        self.assertTrue(validate_ocr_result("1,234,567", 0.9))
        
        # Invalid results
        self.assertFalse(validate_ocr_result("", 0.8))
        self.assertFalse(validate_ocr_result("12345", 0.3))  # Low confidence
        self.assertFalse(validate_ocr_result("99999999999", 0.8))  # Too large

class TestConcurrentProcessor(unittest.TestCase):
    """Test concurrent processing system"""
    
    def test_processing_job_creation(self):
        """Test processing job data structure"""
        from concurrent_processor import ProcessingJob, ProcessingStatus
        from datetime import datetime
        
        job = ProcessingJob(
            job_id="test_123",
            user_id="user_456",
            message_id="msg_789",
            image_data=None,
            player_id=12345,
            created_at=datetime.now()
        )
        
        self.assertEqual(job.job_id, "test_123")
        self.assertEqual(job.status, ProcessingStatus.QUEUED)

class TestTroopMapping(unittest.TestCase):
    """Test troop mapping fix"""
    
    def test_mapping_consistency(self):
        """Test that troop mapping is consistent"""
        from config import (
            TIER_MAPPING, TYPE_MAPPING, 
            SPREADSHEET_TYPE_ORDER, SPREADSHEET_TIER_ORDER
        )
        
        # Test that all tier IDs in order exist in mapping
        for tier_id in SPREADSHEET_TIER_ORDER:
            self.assertIn(tier_id, TIER_MAPPING)
        
        # Test that all type IDs in order exist in mapping
        for type_id in SPREADSHEET_TYPE_ORDER:
            self.assertIn(type_id, TYPE_MAPPING)
    
    def test_flat_counts_generation(self):
        """Test flat counts generation matches spreadsheet order"""
        from config import SPREADSHEET_TYPE_ORDER, SPREADSHEET_TIER_ORDER
        
        # Simulate troop counts
        troop_counts = {
            tier_id: {type_id: (tier_id * 10 + type_id) for type_id in SPREADSHEET_TYPE_ORDER}
            for tier_id in SPREADSHEET_TIER_ORDER
        }
        
        # Generate flat counts like extractor does
        flat_counts = [troop_counts[tier][type_] for tier in SPREADSHEET_TIER_ORDER for type_ in SPREADSHEET_TYPE_ORDER]
        
        # Should have 20 elements (5 tiers * 4 types)
        self.assertEqual(len(flat_counts), 20)
        
        # First element should be T1 Infantry (tier 5, type 1)
        self.assertEqual(flat_counts[0], 51)  # 5*10 + 1

class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    def test_system_startup_sequence(self):
        """Test that all systems can be imported and initialized"""
        try:
            # Test imports
            import config
            import model_manager
            import performance_monitor
            import webhook_logger
            import memory_manager
            import resource_pool
            import enhanced_ocr
            import concurrent_processor
            import connection_monitor
            
            # Test basic functionality
            from performance_monitor import PerformanceTimer
            with PerformanceTimer("integration_test"):
                pass
            
        except Exception as e:
            self.fail(f"System startup failed: {e}")

class PerformanceBenchmark:
    """Performance benchmarking utilities"""
    
    @staticmethod
    def benchmark_ocr_performance():
        """Benchmark OCR performance"""
        from enhanced_ocr import extract_number_enhanced
        import time
        
        # Create test images with different characteristics
        test_images = []
        
        # Simple number image
        simple_img = np.ones((50, 100), dtype=np.uint8) * 255
        test_images.append(("simple", simple_img))
        
        # Noisy image
        noisy_img = np.random.randint(0, 255, (50, 100), dtype=np.uint8)
        test_images.append(("noisy", noisy_img))
        
        results = {}
        
        for name, img in test_images:
            start_time = time.time()
            try:
                text, confidence, metadata = extract_number_enhanced(img)
                duration = time.time() - start_time
                
                results[name] = {
                    "duration": duration,
                    "text": text,
                    "confidence": confidence,
                    "variants_tried": metadata.get('variants_tried', 0),
                    "configs_tried": metadata.get('configs_tried', 0)
                }
            except Exception as e:
                results[name] = {
                    "error": str(e),
                    "duration": time.time() - start_time
                }
        
        return results
    
    @staticmethod
    def benchmark_memory_usage():
        """Benchmark memory usage"""
        import psutil
        import gc
        
        process = psutil.Process()
        
        # Get initial memory
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform some operations
        from model_manager import get_model_stats
        from performance_monitor import get_performance_stats
        from memory_manager import get_memory_stats
        
        stats = [
            get_model_stats(),
            get_performance_stats(),
            get_memory_stats()
        ]
        
        # Force garbage collection
        gc.collect()
        
        # Get final memory
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        return {
            "initial_memory_mb": initial_memory,
            "final_memory_mb": final_memory,
            "memory_increase_mb": final_memory - initial_memory,
            "stats_collected": len(stats)
        }

def run_all_tests():
    """Run all tests and return results"""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestConfig,
        TestModelManager,
        TestPerformanceMonitor,
        TestWebhookLogger,
        TestMemoryManager,
        TestResourcePool,
        TestEnhancedOCR,
        TestConcurrentProcessor,
        TestTroopMapping,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return {
        "tests_run": result.testsRun,
        "failures": len(result.failures),
        "errors": len(result.errors),
        "success": result.wasSuccessful()
    }

def run_benchmarks():
    """Run performance benchmarks"""
    benchmark = PerformanceBenchmark()
    
    results = {
        "ocr_performance": benchmark.benchmark_ocr_performance(),
        "memory_usage": benchmark.benchmark_memory_usage()
    }
    
    return results

if __name__ == "__main__":
    print("🧪 Running Hall of Heroes Bot Test Suite")
    print("=" * 50)
    
    # Run tests
    test_results = run_all_tests()
    
    print("\n📊 Test Results:")
    print(f"Tests run: {test_results['tests_run']}")
    print(f"Failures: {test_results['failures']}")
    print(f"Errors: {test_results['errors']}")
    print(f"Success: {'✅' if test_results['success'] else '❌'}")
    
    # Run benchmarks
    print("\n⚡ Running Performance Benchmarks...")
    benchmark_results = run_benchmarks()
    
    print("\n📈 Benchmark Results:")
    for category, results in benchmark_results.items():
        print(f"\n{category.replace('_', ' ').title()}:")
        for key, value in results.items():
            print(f"  {key}: {value}")
    
    print("\n🎉 Testing complete!")
