"""
Performance monitoring system for Hall of Heroes Bot
Tracks timing metrics and performance statistics
"""

import time
import threading
from collections import defaultdict, deque
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """Data class for storing performance metrics"""
    operation: str
    duration: float
    timestamp: datetime
    success: bool
    metadata: Optional[Dict[str, Any]] = None

class PerformanceMonitor:
    """
    Thread-safe performance monitoring system
    Tracks operation timings and provides statistics
    """
    
    def __init__(self, max_history: int = 1000):
        """
        Initialize performance monitor
        
        Args:
            max_history: Maximum number of metrics to keep in memory
        """
        self._metrics: deque = deque(maxlen=max_history)
        self._lock = threading.Lock()
        self._operation_stats = defaultdict(list)
        self._start_times = {}  # For tracking ongoing operations
        
    def start_operation(self, operation_id: str, operation_name: str) -> None:
        """
        Start timing an operation
        
        Args:
            operation_id: Unique identifier for this operation instance
            operation_name: Name of the operation being timed
        """
        with self._lock:
            self._start_times[operation_id] = {
                'name': operation_name,
                'start_time': time.time(),
                'timestamp': datetime.now()
            }
    
    def end_operation(self, operation_id: str, success: bool = True, 
                     metadata: Optional[Dict[str, Any]] = None) -> float:
        """
        End timing an operation and record the metric
        
        Args:
            operation_id: Unique identifier for this operation instance
            success: Whether the operation was successful
            metadata: Additional metadata about the operation
            
        Returns:
            float: Duration of the operation in seconds
        """
        end_time = time.time()
        
        with self._lock:
            if operation_id not in self._start_times:
                logger.warning(f"Operation {operation_id} was not started")
                return 0.0
            
            start_info = self._start_times.pop(operation_id)
            duration = end_time - start_info['start_time']
            
            metric = PerformanceMetric(
                operation=start_info['name'],
                duration=duration,
                timestamp=start_info['timestamp'],
                success=success,
                metadata=metadata or {}
            )
            
            self._metrics.append(metric)
            self._operation_stats[start_info['name']].append(duration)
            
            # Keep only recent stats for each operation (last 100)
            if len(self._operation_stats[start_info['name']]) > 100:
                self._operation_stats[start_info['name']] = \
                    self._operation_stats[start_info['name']][-100:]
            
            logger.debug(f"Operation {start_info['name']} completed in {duration:.3f}s")
            return duration
    
    def record_metric(self, operation: str, duration: float, success: bool = True,
                     metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Directly record a performance metric
        
        Args:
            operation: Name of the operation
            duration: Duration in seconds
            success: Whether the operation was successful
            metadata: Additional metadata
        """
        with self._lock:
            metric = PerformanceMetric(
                operation=operation,
                duration=duration,
                timestamp=datetime.now(),
                success=success,
                metadata=metadata or {}
            )
            
            self._metrics.append(metric)
            self._operation_stats[operation].append(duration)
            
            if len(self._operation_stats[operation]) > 100:
                self._operation_stats[operation] = self._operation_stats[operation][-100:]
    
    def get_stats(self, operation: Optional[str] = None, 
                  time_window: Optional[timedelta] = None) -> Dict[str, Any]:
        """
        Get performance statistics
        
        Args:
            operation: Specific operation to get stats for (None for all)
            time_window: Time window to consider (None for all time)
            
        Returns:
            Dict containing performance statistics
        """
        with self._lock:
            metrics = list(self._metrics)
        
        # Filter by time window if specified
        if time_window:
            cutoff_time = datetime.now() - time_window
            metrics = [m for m in metrics if m.timestamp >= cutoff_time]
        
        # Filter by operation if specified
        if operation:
            metrics = [m for m in metrics if m.operation == operation]
        
        if not metrics:
            return {'total_operations': 0}
        
        # Calculate statistics
        durations = [m.duration for m in metrics]
        successful_ops = [m for m in metrics if m.success]
        failed_ops = [m for m in metrics if not m.success]
        
        stats = {
            'total_operations': len(metrics),
            'successful_operations': len(successful_ops),
            'failed_operations': len(failed_ops),
            'success_rate': len(successful_ops) / len(metrics) * 100,
            'avg_duration': sum(durations) / len(durations),
            'min_duration': min(durations),
            'max_duration': max(durations),
            'total_duration': sum(durations)
        }
        
        # Calculate percentiles
        sorted_durations = sorted(durations)
        n = len(sorted_durations)
        if n > 0:
            stats['p50_duration'] = sorted_durations[int(n * 0.5)]
            stats['p90_duration'] = sorted_durations[int(n * 0.9)]
            stats['p95_duration'] = sorted_durations[int(n * 0.95)]
            stats['p99_duration'] = sorted_durations[int(n * 0.99)]
        
        # Group by operation if not filtered
        if not operation:
            operation_stats = defaultdict(list)
            for metric in metrics:
                operation_stats[metric.operation].append(metric.duration)
            
            stats['by_operation'] = {}
            for op_name, op_durations in operation_stats.items():
                if op_durations:
                    stats['by_operation'][op_name] = {
                        'count': len(op_durations),
                        'avg_duration': sum(op_durations) / len(op_durations),
                        'min_duration': min(op_durations),
                        'max_duration': max(op_durations)
                    }
        
        return stats
    
    def get_recent_metrics(self, count: int = 10) -> List[PerformanceMetric]:
        """
        Get the most recent performance metrics
        
        Args:
            count: Number of recent metrics to return
            
        Returns:
            List of recent performance metrics
        """
        with self._lock:
            return list(self._metrics)[-count:]
    
    def clear_metrics(self) -> None:
        """Clear all stored metrics"""
        with self._lock:
            self._metrics.clear()
            self._operation_stats.clear()
            self._start_times.clear()
        logger.info("Performance metrics cleared")

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

class PerformanceTimer:
    """Context manager for timing operations"""
    
    def __init__(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None):
        self.operation_name = operation_name
        self.metadata = metadata or {}
        self.operation_id = f"{operation_name}_{time.time()}_{id(self)}"
        self.success = True
    
    def __enter__(self):
        performance_monitor.start_operation(self.operation_id, self.operation_name)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.success = exc_type is None
        if exc_type:
            self.metadata['error'] = str(exc_val)
        
        duration = performance_monitor.end_operation(
            self.operation_id, 
            self.success, 
            self.metadata
        )
        
        if not self.success:
            logger.warning(f"Operation {self.operation_name} failed after {duration:.3f}s: {exc_val}")

def time_operation(operation_name: str, metadata: Optional[Dict[str, Any]] = None):
    """
    Decorator for timing function calls
    
    Args:
        operation_name: Name of the operation being timed
        metadata: Additional metadata to record
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceTimer(operation_name, metadata):
                return func(*args, **kwargs)
        return wrapper
    return decorator

# Convenience functions
def get_performance_stats(operation: Optional[str] = None, 
                         hours: Optional[int] = None) -> Dict[str, Any]:
    """Get performance statistics"""
    time_window = timedelta(hours=hours) if hours else None
    return performance_monitor.get_stats(operation, time_window)

def clear_performance_metrics() -> None:
    """Clear all performance metrics"""
    performance_monitor.clear_metrics()
