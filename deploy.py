"""
Deployment Automation Script for Hall of Heroes Bot
Handles environment setup, dependency installation, and configuration validation
"""

import os
import sys
import subprocess
import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeploymentManager:
    """
    Manages bot deployment process
    """
    
    def __init__(self, project_root: Optional[Path] = None):
        self.project_root = project_root or Path.cwd()
        self.requirements_file = self.project_root / "requirements.txt"
        self.config_file = self.project_root / "config.py"
        self.env_file = self.project_root / ".env"
        
        # Required environment variables
        self.required_env_vars = [
            "DISCORD_TOKEN",
            "GUILD_ID",
            "ERROR_WEBHOOK_URL",
            "STATUS_WEBHOOK_URL",
            "REVIEW_WEBHOOK_URL"
        ]
        
        # Required files
        self.required_files = [
            "main.py",
            "config.py",
            "extractor.py",
            "util.py",
            "model_manager.py",
            "performance_monitor.py",
            "webhook_logger.py",
            "memory_manager.py",
            "resource_pool.py",
            "enhanced_ocr.py",
            "concurrent_processor.py",
            "connection_monitor.py",
            "weights/best.pt",
            "service-account.json"
        ]
        
        # Python dependencies
        self.dependencies = [
            "discord.py>=2.3.0",
            "aiohttp>=3.8.0",
            "python-dotenv>=1.0.0",
            "Pillow>=10.0.0",
            "opencv-python>=4.8.0",
            "numpy>=1.24.0",
            "ultralytics>=8.0.0",
            "pytesseract>=0.3.10",
            "gspread>=5.10.0",
            "psutil>=5.9.0",
            "structlog>=22.3.0"
        ]
    
    def check_python_version(self) -> bool:
        """Check if Python version is compatible"""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            logger.error(f"Python 3.8+ required, found {version.major}.{version.minor}")
            return False
        
        logger.info(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    
    def check_required_files(self) -> Tuple[bool, List[str]]:
        """Check if all required files exist"""
        missing_files = []
        
        for file_path in self.required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            logger.error(f"❌ Missing required files: {missing_files}")
            return False, missing_files
        
        logger.info("✅ All required files present")
        return True, []
    
    def create_requirements_file(self) -> bool:
        """Create requirements.txt file"""
        try:
            with open(self.requirements_file, 'w') as f:
                for dep in self.dependencies:
                    f.write(f"{dep}\n")
            
            logger.info(f"✅ Created {self.requirements_file}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to create requirements.txt: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """Install Python dependencies"""
        try:
            logger.info("📦 Installing Python dependencies...")
            
            # Upgrade pip first
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            # Install dependencies
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file)], 
                         check=True, capture_output=True)
            
            logger.info("✅ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install dependencies: {e}")
            return False
    
    def check_environment_variables(self) -> Tuple[bool, List[str]]:
        """Check if required environment variables are set"""
        missing_vars = []
        
        for var in self.required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.warning(f"⚠️  Missing environment variables: {missing_vars}")
            return False, missing_vars
        
        logger.info("✅ All required environment variables are set")
        return True, []
    
    def create_env_template(self) -> bool:
        """Create .env template file"""
        try:
            env_template = """# Hall of Heroes Bot Configuration
# Copy this file to .env and fill in your values

# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
GUILD_ID=your_guild_id_here

# Discord Webhook URLs for Logging
ERROR_WEBHOOK_URL=your_error_webhook_url_here
STATUS_WEBHOOK_URL=your_status_webhook_url_here
REVIEW_WEBHOOK_URL=your_review_webhook_url_here

# Optional: Override default paths
# YOLO_MODEL_PATH=weights/best.pt
# SERVICE_ACCOUNT_FILE=service-account.json
# SPREADSHEET_NAME=KvK Discord Bot Stats
"""
            
            template_file = self.project_root / ".env.template"
            with open(template_file, 'w') as f:
                f.write(env_template)
            
            logger.info(f"✅ Created {template_file}")
            logger.info("📝 Please copy .env.template to .env and configure your values")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to create .env template: {e}")
            return False
    
    def validate_configuration(self) -> bool:
        """Validate bot configuration"""
        try:
            # Try to import config
            sys.path.insert(0, str(self.project_root))
            import config
            
            # Check critical configuration
            if not hasattr(config, 'TIER_MAPPING') or not config.TIER_MAPPING:
                logger.error("❌ TIER_MAPPING not configured")
                return False
            
            if not hasattr(config, 'TYPE_MAPPING') or not config.TYPE_MAPPING:
                logger.error("❌ TYPE_MAPPING not configured")
                return False
            
            # Check paths
            if not config.YOLO_MODEL_PATH.exists():
                logger.error(f"❌ YOLO model not found: {config.YOLO_MODEL_PATH}")
                return False
            
            if not config.SERVICE_ACCOUNT_FILE.exists():
                logger.error(f"❌ Service account file not found: {config.SERVICE_ACCOUNT_FILE}")
                return False
            
            logger.info("✅ Configuration validation passed")
            return True
        except ImportError as e:
            logger.error(f"❌ Failed to import config: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Configuration validation failed: {e}")
            return False
    
    def check_external_dependencies(self) -> bool:
        """Check external dependencies (Tesseract)"""
        try:
            # Check if Tesseract is installed
            result = subprocess.run(["tesseract", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ Tesseract OCR is installed")
                return True
            else:
                logger.error("❌ Tesseract OCR not found")
                return False
        except FileNotFoundError:
            logger.error("❌ Tesseract OCR not found in PATH")
            logger.info("📝 Please install Tesseract OCR:")
            logger.info("   Windows: https://github.com/UB-Mannheim/tesseract/wiki")
            logger.info("   Linux: sudo apt-get install tesseract-ocr")
            logger.info("   macOS: brew install tesseract")
            return False
    
    def create_startup_script(self) -> bool:
        """Create startup script"""
        try:
            # Windows batch file
            bat_content = """@echo off
echo Starting Hall of Heroes Bot...
cd /d "%~dp0"
python main.py
pause
"""
            
            with open(self.project_root / "start_bot.bat", 'w') as f:
                f.write(bat_content)
            
            # Linux/Mac shell script
            sh_content = """#!/bin/bash
echo "Starting Hall of Heroes Bot..."
cd "$(dirname "$0")"
python3 main.py
"""
            
            sh_file = self.project_root / "start_bot.sh"
            with open(sh_file, 'w') as f:
                f.write(sh_content)
            
            # Make shell script executable
            os.chmod(sh_file, 0o755)
            
            logger.info("✅ Created startup scripts (start_bot.bat, start_bot.sh)")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to create startup scripts: {e}")
            return False
    
    def run_tests(self) -> bool:
        """Run test suite"""
        try:
            logger.info("🧪 Running test suite...")
            
            # Import and run tests
            from test_framework import run_all_tests
            results = run_all_tests()
            
            if results['success']:
                logger.info("✅ All tests passed")
                return True
            else:
                logger.error(f"❌ Tests failed: {results['failures']} failures, {results['errors']} errors")
                return False
        except Exception as e:
            logger.error(f"❌ Failed to run tests: {e}")
            return False
    
    def deploy(self, skip_tests: bool = False) -> bool:
        """Run full deployment process"""
        logger.info("🚀 Starting Hall of Heroes Bot Deployment")
        logger.info("=" * 50)
        
        steps = [
            ("Checking Python version", self.check_python_version),
            ("Checking required files", lambda: self.check_required_files()[0]),
            ("Creating requirements.txt", self.create_requirements_file),
            ("Installing dependencies", self.install_dependencies),
            ("Checking external dependencies", self.check_external_dependencies),
            ("Creating .env template", self.create_env_template),
            ("Validating configuration", self.validate_configuration),
            ("Creating startup scripts", self.create_startup_script),
        ]
        
        if not skip_tests:
            steps.append(("Running tests", self.run_tests))
        
        failed_steps = []
        
        for step_name, step_func in steps:
            logger.info(f"\n📋 {step_name}...")
            try:
                if not step_func():
                    failed_steps.append(step_name)
            except Exception as e:
                logger.error(f"❌ {step_name} failed with exception: {e}")
                failed_steps.append(step_name)
        
        logger.info("\n" + "=" * 50)
        if failed_steps:
            logger.error(f"❌ Deployment completed with {len(failed_steps)} failed steps:")
            for step in failed_steps:
                logger.error(f"   - {step}")
            return False
        else:
            logger.info("🎉 Deployment completed successfully!")
            logger.info("\n📝 Next steps:")
            logger.info("   1. Configure your .env file with Discord tokens and webhook URLs")
            logger.info("   2. Ensure your YOLO model (weights/best.pt) is in place")
            logger.info("   3. Add your Google Sheets service account JSON file")
            logger.info("   4. Run the bot with: python main.py")
            logger.info("   5. Or use the startup scripts: start_bot.bat (Windows) or ./start_bot.sh (Linux/Mac)")
            return True

def main():
    """Main deployment function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Deploy Hall of Heroes Bot")
    parser.add_argument("--skip-tests", action="store_true", 
                       help="Skip running tests during deployment")
    parser.add_argument("--project-root", type=Path, 
                       help="Project root directory (default: current directory)")
    
    args = parser.parse_args()
    
    deployer = DeploymentManager(args.project_root)
    success = deployer.deploy(skip_tests=args.skip_tests)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
