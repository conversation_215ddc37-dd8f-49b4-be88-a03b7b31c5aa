"""
Test script to verify the troop type mapping fix
"""

from config import (
    TIER_MAPPING, TYPE_MAPPING, 
    SPREADSHEET_TYPE_ORDER, SPREADSHEET_TIER_ORDER,
    get_spreadsheet_headers
)

def test_mapping_consistency():
    """Test that the mapping is consistent between extractor and spreadsheet"""
    
    print("=== Troop Type Mapping Test ===")
    print(f"TIER_MAPPING: {TIER_MAPPING}")
    print(f"TYPE_MAPPING: {TYPE_MAPPING}")
    print(f"SPREADSHEET_TYPE_ORDER: {SPREADSHEET_TYPE_ORDER}")
    print(f"SPREADSHEET_TIER_ORDER: {SPREADSHEET_TIER_ORDER}")
    
    # Generate headers
    headers = get_spreadsheet_headers()
    print(f"\nGenerated Headers: {headers}")
    
    # Simulate troop counts (like what extractor.py would generate)
    # Let's say we have:
    # - 100 Infantry (type_id=1)
    # - 200 Cavalry (type_id=2) 
    # - 300 Archers (type_id=3)
    # - 400 Siege (type_id=4)
    # For T1 tier (tier_id=5)
    
    troop_counts = {
        5: {1: 100, 2: 200, 3: 300, 4: 400},  # T1
        6: {1: 0, 2: 0, 3: 0, 4: 0},          # T2
        7: {1: 0, 2: 0, 3: 0, 4: 0},          # T3
        8: {1: 0, 2: 0, 3: 0, 4: 0},          # T4
        9: {1: 0, 2: 0, 3: 0, 4: 0},          # T5
    }
    
    # Generate flat_counts like extractor.py does
    flat_counts = [troop_counts[tier][type_] for tier in SPREADSHEET_TIER_ORDER for type_ in SPREADSHEET_TYPE_ORDER]
    
    print(f"\nSimulated troop_counts: {troop_counts}")
    print(f"Generated flat_counts: {flat_counts}")
    
    # Map flat_counts to headers
    print(f"\nMapping verification:")
    for i, (header, count) in enumerate(zip(headers[1:], flat_counts)):  # Skip 'Player ID'
        if count > 0:
            print(f"  {header}: {count}")
    
    # Verify the mapping is correct
    expected_mapping = {
        "T1 Infantry": 100,
        "T1 Cavalry": 200, 
        "T1 Archers": 300,
        "T1 Siege": 400
    }
    
    print(f"\nExpected mapping: {expected_mapping}")
    
    # Check if our generated data matches expectations
    success = True
    for i, (header, count) in enumerate(zip(headers[1:], flat_counts)):
        if header in expected_mapping:
            if count != expected_mapping[header]:
                print(f"❌ ERROR: {header} expected {expected_mapping[header]}, got {count}")
                success = False
            else:
                print(f"✅ CORRECT: {header} = {count}")
    
    if success:
        print(f"\n🎉 SUCCESS: Troop type mapping is now CORRECT!")
        print(f"   Infantry data will go to Infantry columns")
        print(f"   Cavalry data will go to Cavalry columns") 
        print(f"   Archers data will go to Archers columns")
        print(f"   Siege data will go to Siege columns")
    else:
        print(f"\n❌ FAILURE: Mapping is still incorrect")
    
    return success

if __name__ == "__main__":
    test_mapping_consistency()
