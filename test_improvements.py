"""
Comprehensive test suite for Hall of Heroes Bot improvements
Tests all the implemented enhancements
"""

import asyncio
import time
from datetime import datetime

def test_configuration_system():
    """Test the centralized configuration system"""
    print("=== Testing Configuration System ===")
    
    try:
        from config import (
            TIER_MAPPING, TYPE_MAPPING, SPREADSHEET_TYPE_ORDER,
            get_spreadsheet_headers, YOLO_MODEL_PATH
        )
        
        print("✅ Configuration imports successful")
        print(f"   TIER_MAPPING: {TIER_MAPPING}")
        print(f"   TYPE_MAPPING: {TYPE_MAPPING}")
        print(f"   SPREADSHEET_TYPE_ORDER: {SPREADSHEET_TYPE_ORDER}")
        
        headers = get_spreadsheet_headers()
        print(f"   Generated headers: {len(headers)} columns")
        print(f"   First few headers: {headers[:5]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False

def test_model_manager():
    """Test the model manager and caching system"""
    print("\n=== Testing Model Manager ===")
    
    try:
        from model_manager import model_manager, get_model_stats
        
        print("✅ Model manager imports successful")
        
        # Test stats
        stats = get_model_stats()
        print(f"   Model stats: {stats}")
        
        # Test if model path exists
        from config import YOLO_MODEL_PATH
        if YOLO_MODEL_PATH.exists():
            print(f"✅ YOLO model file found: {YOLO_MODEL_PATH}")
        else:
            print(f"⚠️  YOLO model file not found: {YOLO_MODEL_PATH}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model manager test failed: {str(e)}")
        return False

def test_performance_monitoring():
    """Test the performance monitoring system"""
    print("\n=== Testing Performance Monitoring ===")
    
    try:
        from performance_monitor import (
            PerformanceTimer, get_performance_stats, 
            performance_monitor
        )
        
        print("✅ Performance monitor imports successful")
        
        # Test performance timer
        with PerformanceTimer("test_operation") as timer:
            time.sleep(0.1)  # Simulate work
        
        print("✅ Performance timer test completed")
        
        # Get stats
        stats = get_performance_stats()
        print(f"   Performance stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance monitoring test failed: {str(e)}")
        return False

def test_webhook_logging():
    """Test the webhook logging system"""
    print("\n=== Testing Webhook Logging ===")
    
    try:
        from webhook_logger import log_info, log_warning, log_error
        
        print("✅ Webhook logger imports successful")
        
        # Test logging functions (won't actually send if no URLs configured)
        log_info("Test info message", {"test": True})
        log_warning("Test warning message", {"test": True})
        
        print("✅ Webhook logging functions work")
        
        return True
        
    except Exception as e:
        print(f"❌ Webhook logging test failed: {str(e)}")
        return False

async def test_connection_monitoring():
    """Test the connection monitoring system"""
    print("\n=== Testing Connection Monitoring ===")
    
    try:
        from connection_monitor import (
            connection_monitor, get_health_summary,
            ServiceType, ConnectionStatus
        )
        
        print("✅ Connection monitor imports successful")
        
        # Test health summary
        summary = get_health_summary()
        print(f"   Health summary: {summary}")
        
        # Test service types
        print(f"   Monitored services: {[s.value for s in ServiceType]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection monitoring test failed: {str(e)}")
        return False

def test_troop_mapping_fix():
    """Test that the troop mapping fix is working"""
    print("\n=== Testing Troop Mapping Fix ===")
    
    try:
        # Run the mapping test
        from test_mapping import test_mapping_consistency
        
        success = test_mapping_consistency()
        if success:
            print("✅ Troop mapping fix verified")
            return True
        else:
            print("❌ Troop mapping fix failed")
            return False
            
    except Exception as e:
        print(f"❌ Troop mapping test failed: {str(e)}")
        return False

def test_file_structure():
    """Test that all new files are properly created"""
    print("\n=== Testing File Structure ===")
    
    expected_files = [
        "config.py",
        "model_manager.py", 
        "performance_monitor.py",
        "webhook_logger.py",
        "connection_monitor.py",
        "test_mapping.py",
        "test_webhook.py"
    ]
    
    missing_files = []
    for file in expected_files:
        try:
            with open(file, 'r') as f:
                pass
            print(f"✅ {file} exists")
        except FileNotFoundError:
            print(f"❌ {file} missing")
            missing_files.append(file)
    
    if not missing_files:
        print("✅ All expected files present")
        return True
    else:
        print(f"❌ Missing files: {missing_files}")
        return False

async def run_all_tests():
    """Run all improvement tests"""
    print("🚀 Hall of Heroes Bot - Improvement Test Suite")
    print("=" * 60)
    
    test_results = []
    
    # Run synchronous tests
    test_results.append(("Configuration System", test_configuration_system()))
    test_results.append(("Model Manager", test_model_manager()))
    test_results.append(("Performance Monitoring", test_performance_monitoring()))
    test_results.append(("Webhook Logging", test_webhook_logging()))
    test_results.append(("Troop Mapping Fix", test_troop_mapping_fix()))
    test_results.append(("File Structure", test_file_structure()))
    
    # Run asynchronous tests
    test_results.append(("Connection Monitoring", await test_connection_monitoring()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Improvements are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    print(f"\nTest completed at: {datetime.now()}")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(run_all_tests())
