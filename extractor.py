import cv2
import pytesseract
import numpy as np

from PIL import Image
from ultralytics import YOL<PERSON>
from util import TroopDetection
from config import (
    TIER_MAPPING, TYPE_MAPPING,
    TROOP_TIER_CLASSES, TROOP_TYPE_CLASSES, AMOUNT_CLASSES,
    SPREADSHEET_TYPE_ORDER, SPREADSHEET_TIER_ORDER
)
from model_manager import get_model
from performance_monitor import PerformanceTimer
from webhook_logger import log_info, log_error, log_warning
from resource_pool import get_image_cache
import hashlib

def extract_deads(image):
    """
    Extract dead troops from an image with caching and memory optimization.

    Args:
        image: The input image.

    Returns:
        tuple: List of dead troop counts (T1 to T5), description, processed image.
    """
    with PerformanceTimer("extract_deads_total"):
        # Generate cache key from image
        image_bytes = image.tobytes() if hasattr(image, 'tobytes') else str(image).encode()
        cache_key = hashlib.md5(image_bytes).hexdigest()

        # Check cache first
        cache = get_image_cache()
        cached_result = cache.get(cache_key)
        if cached_result:
            log_info("Using cached extraction result", {"cache_key": cache_key[:8]})
            return cached_result

        # Create an instance of TroopDetection
        from util import TroopDetection
        troop_detection = TroopDetection()

        # Use cached model for better performance
        with PerformanceTimer("model_loading"):
            model = get_model()

        troop_tiers = TROOP_TIER_CLASSES
        troop_types = TROOP_TYPE_CLASSES
        amounts = AMOUNT_CLASSES

        troop_tiers_detections = []
        troop_types_detections = []
        troop_amounts_detections = []
        boxes = []

        # Use centralized configuration
        tiers = TIER_MAPPING
        types = TYPE_MAPPING

        # Log configuration for debugging (only in debug mode)
        # print(f"Tiers dictionary: {tiers}")
        # print(f"Types dictionary: {types}")

        # Run YOLO inference with performance monitoring
        with PerformanceTimer("yolo_inference"):
            results = model(image)[0]

        # Create a copy of the image for visualization
        im_array = results.plot()

        # Populate detections
        for result in results.boxes.data.tolist():
            x1, y1, x2, y2, score, class_id = result

            if int(class_id) in troop_tiers:
                troop_tiers_detections.append([x1, y1, x2, y2, class_id])
            elif int(class_id) in troop_types:
                troop_types_detections.append([x1, y1, x2, y2, class_id])
            elif int(class_id) in amounts:
                troop_amounts_detections.append([x1, y1, x2, y2])
            else:
                boxes.append([x1, y1, x2, y2])

        # Log detection results for monitoring
        detection_summary = {
            "tier_detections": len(troop_tiers_detections),
            "type_detections": len(troop_types_detections),
            "amount_detections": len(troop_amounts_detections),
            "total_boxes": len(boxes)
        }
        log_info("YOLO detection completed", detection_summary)

        # Initialize variables to store troop counts using configuration
        troop_counts = {tier_id: {type_id: 0 for type_id in TROOP_TYPE_CLASSES}
                        for tier_id in TROOP_TIER_CLASSES}

        # Initialize description and flat_counts
        description = ""
        flat_counts = [0] * 20  # Initialize with zeros for all possible troop combinations

        # Process each detected box with performance monitoring
        with PerformanceTimer("ocr_processing"):
            for i, box in enumerate(boxes):
                # Get amount, type, and tier for this box
                x1_amount, y1_amount, x2_amount, y2_amount, type_, tier = troop_detection.get_amount(
                    troop_tiers_detections, troop_types_detections, troop_amounts_detections, box
                )

                # Skip if we couldn't find all the necessary data
                if x1_amount is None or y1_amount is None or x2_amount is None or y2_amount is None or type_ is None or tier is None:
                    log_warning("Skipping detection due to missing data", {
                        "box_coordinates": box,
                        "tier": tier,
                        "type": type_,
                        "amount_coords": [x1_amount, y1_amount, x2_amount, y2_amount]
                    })
                    continue

                amount_crop = im_array[int(y1_amount):int(y2_amount), int(x1_amount):int(x2_amount)]
                amount_crop_gray = cv2.cvtColor(amount_crop, cv2.COLOR_BGR2GRAY)
                _, amount_crop_thresh = cv2.threshold(amount_crop_gray, 100, 255, cv2.THRESH_BINARY)
                amount_crop_thresh = np.array(amount_crop_thresh)
                dead_amount = troop_detection.read_amount(amount_crop_thresh)

                try:
                    # Change troop_tier to tier and troop_type to type_
                    troop_counts[int(tier)][int(type_)] += int(dead_amount)
                except (ValueError, KeyError) as e:
                    log_error("Error processing troop count", e, {
                        "tier": tier,
                        "type": type_,
                        "dead_amount": dead_amount,
                        "box_coordinates": box
                    })
                    continue

        # Construct description
        for tier in sorted(troop_counts.keys()):
            for type_, count in troop_counts[tier].items():
                if count > 0:
                    # Before adding to description, check if tier and type are valid keys
                    if tier in tiers and type_ in types:
                        description += f"{tiers[tier]} {types[type_]}: {count}\n"
                        # Update the flat_counts array with the count
                        index = (tier - 5) * 4 + (type_ - 1)
                        if 0 <= index < len(flat_counts):
                            flat_counts[index] = count
                    else:
                        log_warning("Invalid tier or type detected", {
                            "tier": tier,
                            "type": type_,
                            "valid_tiers": list(tiers.keys()),
                            "valid_types": list(types.keys())
                        })

        # Update flat_counts with the actual values from troop_counts in correct spreadsheet order
        flat_counts = [troop_counts[tier][type_] for tier in SPREADSHEET_TIER_ORDER for type_ in SPREADSHEET_TYPE_ORDER]

        # Log final results
        total_troops = sum(flat_counts)
        log_info("Troop extraction completed", {
            "total_troops_detected": total_troops,
            "description_length": len(description),
            "non_zero_counts": sum(1 for count in flat_counts if count > 0)
        })

        # Draw additional information on the image with performance monitoring
        with PerformanceTimer("image_annotation"):
            for box in boxes:
                x1_box, y1_box, x2_box, y2_box = box
                # Draw a green box
                cv2.rectangle(im_array, (int(x1_box), int(y1_box)), (int(x2_box), int(y2_box)), (0, 255, 0), 2)

            for detection in troop_tiers_detections:
                x1, y1, x2, y2, class_id = detection
                # Draw a blue box for tier
                cv2.rectangle(im_array, (int(x1), int(y1)), (int(x2), int(y2)), (255, 0, 0), 2)
                # Add tier label
                cv2.putText(im_array, f"Tier: {tiers[int(class_id)]}", (int(x1), int(y1)-10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

            for detection in troop_types_detections:
                x1, y1, x2, y2, class_id = detection
                # Draw a red box for type
                cv2.rectangle(im_array, (int(x1), int(y1)), (int(x2), int(y2)), (0, 0, 255), 2)
                # Add type label
                cv2.putText(im_array, f"Type: {types[int(class_id)]}", (int(x1), int(y1)-10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

            for coords in troop_amounts_detections:
                x1, y1, x2, y2 = coords
                # Draw a yellow box for amount
                cv2.rectangle(im_array, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 255), 2)
                # Add amount label
                cv2.putText(im_array, "Amount", (int(x1), int(y1)-10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)

            # Convert the processed image to PIL format for Discord
            processed_image = Image.fromarray(cv2.cvtColor(im_array, cv2.COLOR_BGR2RGB))

        # Cache the result for future use
        result = (flat_counts, description, processed_image)
        cache.put(cache_key, result)
        log_info("Cached extraction result", {
            "cache_key": cache_key[:8],
            "total_troops": sum(flat_counts)
        })

        return result
