"""
Enhanced OCR System for Hall of Heroes Bot
Implements multiple OCR engines and advanced preprocessing for better accuracy
"""

import cv2
import numpy as np
import pytesseract
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import logging
import re
from collections import Counter

from config import TESSERACT_CMD, CONFIDENCE_THRESHOLD
from performance_monitor import PerformanceTimer
from webhook_logger import log_warning, log_info

logger = logging.getLogger(__name__)

# Set Tesseract path
pytesseract.pytesseract.tesseract_cmd = TESSERACT_CMD

@dataclass
class OCRResult:
    """OCR result with confidence score"""
    text: str
    confidence: float
    engine: str
    preprocessing: str

class ImagePreprocessor:
    """
    Advanced image preprocessing for better OCR accuracy
    """
    
    @staticmethod
    def enhance_contrast(image: np.ndarray) -> np.ndarray:
        """Enhance image contrast using CLAHE"""
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        return clahe.apply(image)
    
    @staticmethod
    def denoise(image: np.ndarray) -> np.ndarray:
        """Remove noise from image"""
        return cv2.fastNlMeansDenoising(image)
    
    @staticmethod
    def sharpen(image: np.ndarray) -> np.ndarray:
        """Sharpen image using unsharp masking"""
        gaussian = cv2.GaussianBlur(image, (0, 0), 2.0)
        return cv2.addWeighted(image, 1.5, gaussian, -0.5, 0)
    
    @staticmethod
    def morphological_operations(image: np.ndarray) -> np.ndarray:
        """Apply morphological operations to clean up text"""
        kernel = np.ones((2, 2), np.uint8)
        # Opening to remove noise
        opening = cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel, iterations=1)
        # Closing to fill gaps
        closing = cv2.morphologyEx(opening, cv2.MORPH_CLOSE, kernel, iterations=1)
        return closing
    
    @staticmethod
    def adaptive_threshold(image: np.ndarray) -> np.ndarray:
        """Apply adaptive thresholding"""
        return cv2.adaptiveThreshold(
            image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
    
    @staticmethod
    def otsu_threshold(image: np.ndarray) -> np.ndarray:
        """Apply Otsu's thresholding"""
        _, thresh = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        return thresh
    
    def preprocess_variants(self, image: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Generate optimized preprocessed variants (reduced for speed)

        Returns:
            Dict mapping variant name to processed image
        """
        variants = {}

        # Original (just grayscale)
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        variants['original'] = gray

        # Enhanced contrast (most effective)
        enhanced = self.enhance_contrast(gray)
        variants['enhanced'] = enhanced

        # Otsu threshold (fast and effective)
        otsu = self.otsu_threshold(gray)
        variants['otsu'] = otsu

        # Enhanced + Otsu (best combination)
        enhanced_otsu = self.otsu_threshold(enhanced)
        variants['enhanced_otsu'] = enhanced_otsu

        # Scaled up version (only for enhanced_otsu - most effective)
        scaled = cv2.resize(enhanced_otsu, None, fx=2, fy=2, interpolation=cv2.INTER_CUBIC)
        variants['enhanced_otsu_scaled'] = scaled

        return variants

class EnhancedOCR:
    """
    Enhanced OCR system with multiple engines and preprocessing
    """
    
    def __init__(self):
        self.preprocessor = ImagePreprocessor()
        
        # Optimized Tesseract configurations (reduced for speed)
        self.tesseract_configs = [
            r'--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789,.',  # Single text line (most common)
            r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789,.',  # Single word
            r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789,.',  # General numbers
        ]
    
    def extract_with_tesseract(self, image: np.ndarray, config: str) -> Tuple[str, float]:
        """
        Extract text using Tesseract with confidence score
        
        Returns:
            Tuple of (text, confidence)
        """
        try:
            # Get text and confidence data
            data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)
            
            # Filter out low confidence detections
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            texts = [text.strip() for i, text in enumerate(data['text']) 
                    if int(data['conf'][i]) > 0 and text.strip()]
            
            if not texts or not confidences:
                return "", 0.0
            
            # Combine text and calculate average confidence
            combined_text = ' '.join(texts)
            avg_confidence = sum(confidences) / len(confidences) / 100.0  # Convert to 0-1 range
            
            return combined_text, avg_confidence
            
        except Exception as e:
            logger.error(f"Tesseract extraction failed: {e}")
            return "", 0.0
    
    def clean_numeric_text(self, text: str) -> str:
        """
        Clean and normalize numeric text
        """
        if not text:
            return ""
        
        # Remove non-numeric characters except commas and periods
        cleaned = re.sub(r'[^\d,.]', '', text)
        
        # Handle common OCR errors
        cleaned = cleaned.replace('O', '0')  # O -> 0
        cleaned = cleaned.replace('l', '1')  # l -> 1
        cleaned = cleaned.replace('I', '1')  # I -> 1
        cleaned = cleaned.replace('S', '5')  # S -> 5
        cleaned = cleaned.replace('s', '5')  # s -> 5
        
        # Remove leading/trailing punctuation
        cleaned = cleaned.strip(',.')
        
        # Validate number format
        if not cleaned or not any(c.isdigit() for c in cleaned):
            return ""
        
        # Handle comma formatting
        if ',' in cleaned:
            parts = cleaned.split(',')
            # First part can be any length, subsequent parts should be 3 digits
            if len(parts) > 1:
                valid = True
                for i, part in enumerate(parts[1:], 1):
                    if len(part) != 3:
                        valid = False
                        break
                
                if valid:
                    # Remove commas for final number
                    cleaned = cleaned.replace(',', '')
        
        return cleaned
    
    def extract_number(self, image: np.ndarray) -> Tuple[str, float, Dict[str, Any]]:
        """
        Extract number from image using multiple techniques
        
        Returns:
            Tuple of (best_result, confidence, metadata)
        """
        with PerformanceTimer("enhanced_ocr_extraction"):
            results = []
            metadata = {
                'variants_tried': 0,
                'configs_tried': 0,
                'raw_results': []
            }
            
            # Generate preprocessed variants
            variants = self.preprocessor.preprocess_variants(image)
            metadata['variants_tried'] = len(variants)
            
            # Try each variant with each configuration
            for variant_name, variant_image in variants.items():
                for config in self.tesseract_configs:
                    try:
                        raw_text, confidence = self.extract_with_tesseract(variant_image, config)
                        cleaned_text = self.clean_numeric_text(raw_text)
                        
                        if cleaned_text and confidence > 0:
                            result = OCRResult(
                                text=cleaned_text,
                                confidence=confidence,
                                engine='tesseract',
                                preprocessing=variant_name
                            )
                            results.append(result)
                            
                            metadata['raw_results'].append({
                                'variant': variant_name,
                                'config': config,
                                'raw_text': raw_text,
                                'cleaned_text': cleaned_text,
                                'confidence': confidence
                            })
                        
                        metadata['configs_tried'] += 1
                        
                    except Exception as e:
                        logger.debug(f"OCR attempt failed for {variant_name}: {e}")
            
            # Analyze results and pick the best one
            if not results:
                return "", 0.0, metadata
            
            # Group by text and calculate consensus
            text_groups = {}
            for result in results:
                if result.text not in text_groups:
                    text_groups[result.text] = []
                text_groups[result.text].append(result)
            
            # Find the most confident result with consensus
            best_text = ""
            best_confidence = 0.0
            
            for text, group in text_groups.items():
                # Calculate group confidence (average of all attempts)
                group_confidence = sum(r.confidence for r in group) / len(group)
                
                # Boost confidence for results that appear multiple times
                consensus_boost = min(len(group) / len(results), 0.3)  # Max 30% boost
                final_confidence = min(group_confidence + consensus_boost, 1.0)
                
                if final_confidence > best_confidence:
                    best_confidence = final_confidence
                    best_text = text
            
            metadata['consensus_groups'] = len(text_groups)
            metadata['best_group_size'] = len(text_groups.get(best_text, []))
            
            return best_text, best_confidence, metadata
    
    def validate_result(self, text: str, confidence: float) -> bool:
        """
        Validate OCR result based on confidence and content
        """
        if not text or confidence < CONFIDENCE_THRESHOLD:
            return False
        
        # Check if result looks like a valid number
        try:
            num = int(text.replace(',', ''))
            # Reasonable range for troop counts
            if num < 0 or num > 10000000:  # 10 million max
                return False
        except ValueError:
            return False
        
        return True

# Global enhanced OCR instance
enhanced_ocr = EnhancedOCR()

def extract_number_enhanced(image: np.ndarray) -> Tuple[str, float, Dict[str, Any]]:
    """
    Extract number from image using enhanced OCR
    
    Returns:
        Tuple of (text, confidence, metadata)
    """
    return enhanced_ocr.extract_number(image)

def validate_ocr_result(text: str, confidence: float) -> bool:
    """
    Validate OCR result
    """
    return enhanced_ocr.validate_result(text, confidence)
