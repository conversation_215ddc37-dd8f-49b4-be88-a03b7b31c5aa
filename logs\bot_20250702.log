2025-07-02 03:45:49,407 - discord.client - INFO - logging in using static token
2025-07-02 03:45:50,047 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 080f448468455b4ba68006aeaa167bcd).
2025-07-02 03:46:41,266 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 37, in extract_deads
    print(f"Tiers dictionary: {tiers}")

2025-07-02 03:48:34,030 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 03:48:44,045 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 03:48:54,061 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 03:49:04,076 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 03:49:14,092 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 60 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 03:49:24,107 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 70 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 03:49:27,014 - discord.gateway - INFO - Shard ID None session has been invalidated.
2025-07-02 03:49:32,311 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 83be6efdd68eb4de049fb33a6db2cabe).
2025-07-02 06:49:00,125 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 08:04:03,967 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 10:43:48,383 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 10:53:36,082 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 10:53:46,098 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 10:53:48,676 - discord.client - ERROR - Attempting a reconnect in 1.29s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-02 10:53:50,441 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 11:05:45,577 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 11:07:53,826 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 11:54:04,458 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 12:53:43,511 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 13:38:18,720 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 14:57:39,258 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 17:00:55,702 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-02 17:01:03,515 - discord.client - ERROR - Attempting a reconnect in 1.83s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 659, in connect
    await self.ws.poll_event()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-02 17:01:05,500 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 20:41:19,546 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-02 21:04:08,467 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-03 00:09:31,094 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
2025-07-03 00:11:03,593 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 172, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\discord\client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\hoh 222\main.py", line 128, in on_message
    troop_data, description, processed_image = extract_deads(image)
  File "C:\Users\<USER>\Desktop\hoh 222\extractor.py", line 101, in extract_deads
    dead_amount = troop_detection.read_amount(amount_crop_thresh)
  File "C:\Users\<USER>\Desktop\hoh 222\util.py", line 162, in read_amount
    extracted = pytesseract.image_to_string(img, config=config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 423, in image_to_string
    return {
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 426, in <lambda>
    Output.STRING: lambda: run_and_get_output(*args),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 288, in run_and_get_output
    run_tesseract(**kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 262, in run_tesseract
    with timeout_manager(proc, timeout) as error_string:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 135, in __enter__
    return next(self.gen)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pytesseract\pytesseract.py", line 127, in timeout_manager
    yield proc.communicate()[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\subprocess.py", line 1528, in _communicate
    self.stdout_thread.join(self._remaining_time(endtime))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):

2025-07-03 01:58:07,435 - discord.gateway - INFO - Shard ID None has successfully RESUMED session 83be6efdd68eb4de049fb33a6db2cabe.
