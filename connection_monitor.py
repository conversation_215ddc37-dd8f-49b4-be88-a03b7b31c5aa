"""
Connection Health Monitoring System for Hall of Heroes Bot
Monitors Discord and Google Sheets connections with auto-recovery
"""

import asyncio
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass
import logging

from config import (
    HEALTH_CHECK_INTERVAL, CONNECTION_TIMEOUT, HEARTBEAT_INTERVAL,
    CIRCUIT_BREAKER_THRESHOLD, CIRCUIT_BREAKER_TIMEOUT
)
from webhook_logger import log_error, log_warning, log_info, log_critical

logger = logging.getLogger(__name__)

class ConnectionStatus(Enum):
    """Connection status states"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    DISCONNECTED = "disconnected"
    RECOVERING = "recovering"

class ServiceType(Enum):
    """Types of services to monitor"""
    DISCORD = "discord"
    GOOGLE_SHEETS = "google_sheets"
    YOLO_MODEL = "yolo_model"

@dataclass
class HealthCheck:
    """Health check result"""
    service: ServiceType
    status: ConnectionStatus
    timestamp: datetime
    response_time: float
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class CircuitBreaker:
    """Circuit breaker pattern implementation"""
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "closed"  # closed, open, half-open
    
    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.state == "open":
            if time.time() - self.last_failure_time > self.timeout:
                self.state = "half-open"
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = func(*args, **kwargs)
            self.on_success()
            return result
        except Exception as e:
            self.on_failure()
            raise e
    
    def on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        self.state = "closed"
    
    def on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"

class ConnectionMonitor:
    """
    Monitors connection health for all bot services
    """
    
    def __init__(self):
        self._health_checks: Dict[ServiceType, HealthCheck] = {}
        self._circuit_breakers: Dict[ServiceType, CircuitBreaker] = {}
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
        self._lock = threading.Lock()
        
        # Initialize circuit breakers
        for service in ServiceType:
            self._circuit_breakers[service] = CircuitBreaker(
                CIRCUIT_BREAKER_THRESHOLD, 
                CIRCUIT_BREAKER_TIMEOUT
            )
    
    async def start_monitoring(self):
        """Start the health monitoring loop"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        log_info("Connection monitoring started", {
            "check_interval": HEALTH_CHECK_INTERVAL,
            "services": [s.value for s in ServiceType]
        })
    
    async def stop_monitoring(self):
        """Stop the health monitoring loop"""
        self._monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        log_info("Connection monitoring stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop"""
        while self._monitoring:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(HEALTH_CHECK_INTERVAL)
            except asyncio.CancelledError:
                break
            except Exception as e:
                log_error("Error in monitoring loop", e)
                await asyncio.sleep(5)  # Brief pause before retrying
    
    async def _perform_health_checks(self):
        """Perform health checks for all services"""
        tasks = []
        for service in ServiceType:
            task = asyncio.create_task(self._check_service_health(service))
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _check_service_health(self, service: ServiceType):
        """Check health of a specific service"""
        start_time = time.time()
        
        try:
            if service == ServiceType.DISCORD:
                await self._check_discord_health()
            elif service == ServiceType.GOOGLE_SHEETS:
                await self._check_sheets_health()
            elif service == ServiceType.YOLO_MODEL:
                await self._check_model_health()
            
            response_time = time.time() - start_time
            
            health_check = HealthCheck(
                service=service,
                status=ConnectionStatus.HEALTHY,
                timestamp=datetime.now(),
                response_time=response_time
            )
            
            self._update_health_status(service, health_check)
            
        except Exception as e:
            response_time = time.time() - start_time
            
            health_check = HealthCheck(
                service=service,
                status=ConnectionStatus.UNHEALTHY,
                timestamp=datetime.now(),
                response_time=response_time,
                error_message=str(e)
            )
            
            self._update_health_status(service, health_check)
            
            # Log error for unhealthy services
            log_warning(f"{service.value} health check failed", {
                "service": service.value,
                "error": str(e),
                "response_time": response_time
            })
    
    async def _check_discord_health(self):
        """Check Discord connection health"""
        # This would be implemented with actual Discord client
        # For now, simulate a health check
        await asyncio.sleep(0.1)  # Simulate network call
        # In real implementation: check bot.is_ready(), ping Discord API, etc.
    
    async def _check_sheets_health(self):
        """Check Google Sheets connection health"""
        # This would be implemented with actual Sheets client
        # For now, simulate a health check
        await asyncio.sleep(0.1)  # Simulate network call
        # In real implementation: try to read a cell, check API quota, etc.
    
    async def _check_model_health(self):
        """Check YOLO model health"""
        from model_manager import model_manager
        # Check if model is loaded and responsive
        if not model_manager.is_model_loaded():
            raise Exception("YOLO model not loaded")
        
        # Could add a quick inference test here
        await asyncio.sleep(0.05)  # Simulate model check
    
    def _update_health_status(self, service: ServiceType, health_check: HealthCheck):
        """Update health status for a service"""
        with self._lock:
            previous_status = self._health_checks.get(service)
            self._health_checks[service] = health_check
            
            # Log status changes
            if previous_status and previous_status.status != health_check.status:
                log_info(f"{service.value} status changed", {
                    "service": service.value,
                    "previous_status": previous_status.status.value,
                    "new_status": health_check.status.value,
                    "response_time": health_check.response_time
                })
    
    def get_service_status(self, service: ServiceType) -> Optional[HealthCheck]:
        """Get current health status for a service"""
        with self._lock:
            return self._health_checks.get(service)
    
    def get_all_statuses(self) -> Dict[ServiceType, HealthCheck]:
        """Get health status for all services"""
        with self._lock:
            return self._health_checks.copy()
    
    def is_service_healthy(self, service: ServiceType) -> bool:
        """Check if a service is currently healthy"""
        status = self.get_service_status(service)
        return status and status.status == ConnectionStatus.HEALTHY
    
    def are_all_services_healthy(self) -> bool:
        """Check if all services are healthy"""
        return all(self.is_service_healthy(service) for service in ServiceType)
    
    def get_circuit_breaker(self, service: ServiceType) -> CircuitBreaker:
        """Get circuit breaker for a service"""
        return self._circuit_breakers[service]
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get a summary of all service health"""
        statuses = self.get_all_statuses()
        
        summary = {
            "overall_healthy": self.are_all_services_healthy(),
            "services": {},
            "last_check": datetime.now().isoformat()
        }
        
        for service, health_check in statuses.items():
            circuit_breaker = self._circuit_breakers[service]
            summary["services"][service.value] = {
                "status": health_check.status.value,
                "response_time": health_check.response_time,
                "last_check": health_check.timestamp.isoformat(),
                "circuit_breaker_state": circuit_breaker.state,
                "error": health_check.error_message
            }
        
        return summary

# Global connection monitor instance
connection_monitor = ConnectionMonitor()

# Convenience functions
async def start_health_monitoring():
    """Start connection health monitoring"""
    await connection_monitor.start_monitoring()

async def stop_health_monitoring():
    """Stop connection health monitoring"""
    await connection_monitor.stop_monitoring()

def get_service_health(service: ServiceType) -> Optional[HealthCheck]:
    """Get health status for a specific service"""
    return connection_monitor.get_service_status(service)

def is_service_healthy(service: ServiceType) -> bool:
    """Check if a service is healthy"""
    return connection_monitor.is_service_healthy(service)

def get_health_summary() -> Dict[str, Any]:
    """Get overall health summary"""
    return connection_monitor.get_health_summary()

def get_circuit_breaker(service: ServiceType) -> CircuitBreaker:
    """Get circuit breaker for a service"""
    return connection_monitor.get_circuit_breaker(service)
