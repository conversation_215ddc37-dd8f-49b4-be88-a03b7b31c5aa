import pytesseract
import gspread
import cv2
import numpy as np
from config import (
    SERVICE_ACCOUNT_FILE, SPREADSHEET_NAME, SHEET_NAME,
    TESSERACT_CMD, get_spreadsheet_headers
)
from enhanced_ocr import extract_number_enhanced, validate_ocr_result
from webhook_logger import log_warning, log_info

# Initialize Google Sheets connection
sa = gspread.service_account(filename=str(SERVICE_ACCOUNT_FILE))
pytesseract.pytesseract.tesseract_cmd = TESSERACT_CMD

class TroopDetection:
    def __init__(self, tolerance=0.2):  # Increased from 0.1 to 0.2
        self.tolerance = tolerance


    def get_amount(self, troop_tiers_detections, troop_types_detections, troop_amounts_detections, box):
        """
        Get troop amount, type, and tier from detections with improved matching.
    
        Args:
            troop_tiers_detections: Troop tier detections.
            troop_types_detections: Troop type detections.
            troop_amounts_detections: Troop amount detections.
            box: Bounding box coordinates.
    
        Returns:
            Tuple containing amount coordinates, type, and tier.
        """
        x1_box, y1_box, x2_box, y2_box = box
        x1_amount, y1_amount, x2_amount, y2_amount = None, None, None, None
        type_, tier = None, None
        
        # Calculate box center and dimensions
        box_center_x = (x1_box + x2_box) / 2
        box_center_y = (y1_box + y2_box) / 2
        box_width = x2_box - x1_box
        box_height = y2_box - y1_box
        
        # Increase search radius for different layouts
        search_radius = max(box_width, box_height) * 2.0
        
        # Find closest amount detection with improved alignment checks
        min_dist_amount = float('inf')
        for j in range(len(troop_amounts_detections)):
            x1, y1, x2, y2 = troop_amounts_detections[j]
            amount_center_x = (x1 + x2) / 2
            amount_center_y = (y1 + y2) / 2
            
            # Calculate distance between centers
            dist = ((box_center_x - amount_center_x) ** 2 + (box_center_y - amount_center_y) ** 2) ** 0.5
            
            # Check horizontal alignment (same row)
            horizontal_alignment = abs(box_center_y - amount_center_y) < box_height * 1.0
            
            # Check if the amount is to the right of the troop icon (common layout in game)
            is_to_right = amount_center_x > box_center_x
            
            # Prioritize amounts that are aligned and to the right of the troop
            alignment_score = 1.0
            if horizontal_alignment:
                alignment_score *= 0.5
            if is_to_right:
                alignment_score *= 0.5
                
            # Adjust distance by alignment score
            adjusted_dist = dist * alignment_score
            
            # Check if this amount is the best match so far
            if adjusted_dist < min_dist_amount and dist < search_radius:
                min_dist_amount = adjusted_dist
                x1_amount, y1_amount, x2_amount, y2_amount = int(x1), int(y1), int(x2), int(y2)
        
        # Find closest troop type detection
        min_dist_type = float('inf')
        for i in range(len(troop_types_detections)):
            x1, y1, x2, y2, class_id = troop_types_detections[i]
            type_center_x = (x1 + x2) / 2
            type_center_y = (y1 + y2) / 2
            
            # Calculate distance between centers
            dist = ((box_center_x - type_center_x) ** 2 + (box_center_y - type_center_y) ** 2) ** 0.5
            
            # Check if this type is within the box or close to it
            if dist < min_dist_type and dist < search_radius:
                min_dist_type = dist
                type_ = class_id
        
        # Find closest troop tier detection
        min_dist_tier = float('inf')
        for k in range(len(troop_tiers_detections)):
            x1, y1, x2, y2, class_id = troop_tiers_detections[k]
            tier_center_x = (x1 + x2) / 2
            tier_center_y = (y1 + y2) / 2
            
            # Calculate distance between centers
            dist = ((box_center_x - tier_center_x) ** 2 + (box_center_y - tier_center_y) ** 2) ** 0.5
            
            # Check if this tier is within the box or close to it
            if dist < min_dist_tier and dist < search_radius:
                min_dist_tier = dist
                tier = class_id
        
        return x1_amount, y1_amount, x2_amount, y2_amount, type_, tier

    def read_amount(self, amount_crop):
        """
        Read the troop amount from an image using fast OCR.

        Args:
            amount_crop: Cropped image containing the amount.

        Returns:
            Cleaned troop amount as a string, or '0' if no digits found.
        """
        try:
            # Use fast, simple OCR first
            text = self._fast_ocr(amount_crop)
            if text and text != '0':
                return text

            # Fallback to enhanced OCR only if needed
            text, confidence, metadata = extract_number_enhanced(amount_crop)

            # Validate result
            if validate_ocr_result(text, confidence):
                return text
            else:
                return '0'

        except Exception as e:
            log_warning("OCR failed, using fallback", {"error": str(e)})
            return self._legacy_read_amount(amount_crop)

    def _fast_ocr(self, amount_crop):
        """Fast OCR method for quick processing"""
        try:
            # Simple preprocessing
            height, width = amount_crop.shape
            if height < 30 or width < 30:
                amount_crop = cv2.resize(amount_crop, (width * 2, height * 2), interpolation=cv2.INTER_CUBIC)

            # Simple threshold
            _, thresh = cv2.threshold(amount_crop, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # Quick OCR
            text = pytesseract.image_to_string(thresh, config=r'--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789,.')

            # Clean result
            cleaned = ''.join(c for c in text if c.isdigit() or c in ',.')
            cleaned = cleaned.strip(',.')

            if cleaned and cleaned.replace(',', '').isdigit():
                return cleaned.replace(',', '')

            return '0'

        except Exception:
            return '0'

    def _legacy_read_amount(self, amount_crop):
        """
        Legacy OCR method as fallback
        """
        # Pre-process the image for better OCR
        height, width = amount_crop.shape

        # Create a larger version for better OCR
        amount_crop_large = cv2.resize(amount_crop, (width * 3, height * 3), interpolation=cv2.INTER_CUBIC)
        
        # Apply different preprocessing techniques
        # 1. Original resized image
        images = [amount_crop_large]
        
        # 2. Sharpened image
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(amount_crop_large, -1, kernel)
        images.append(sharpened)
        
        # 3. Binary with Otsu thresholding
        _, binary_otsu = cv2.threshold(amount_crop_large, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        images.append(binary_otsu)
        
        # 4. Adaptive thresholding
        binary_adaptive = cv2.adaptiveThreshold(amount_crop_large, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                               cv2.THRESH_BINARY, 11, 2)
        images.append(binary_adaptive)
        
        # 5. Dilated image to connect broken digits
        kernel = np.ones((2,2), np.uint8)
        dilated = cv2.dilate(binary_otsu, kernel, iterations=1)
        images.append(dilated)
        
        # 6. Eroded image to separate connected digits
        eroded = cv2.erode(binary_otsu, kernel, iterations=1)
        images.append(eroded)
        
        # Try multiple OCR configurations
        configs = [
            r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789,.',
            r'--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789,.',
            r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789,.',
            r'--oem 3 --psm 10 -c tessedit_char_whitelist=0123456789,.',
            r'--oem 1 --psm 7 -c tessedit_char_whitelist=0123456789,.'
        ]
        
        all_results = []
        
        # Try with different preprocessed images and configurations
        for img in images:
            for config in configs:
                extracted = pytesseract.image_to_string(img, config=config)
                # Clean the result - keep only digits and commas
                cleaned = ''.join(c for c in extracted if c.isdigit() or c in '.,')
                if cleaned:
                    # Remove any trailing periods or commas
                    cleaned = cleaned.strip('.,')
                    all_results.append(cleaned)
        
        if not all_results:
            return '0'
        
        # Process results to handle common OCR errors
        processed_results = []
        for result in all_results:
            # First, check if the result has commas in the right places
            # Game numbers typically have commas every 3 digits from the right
            if ',' in result:
                # Split by comma and check if parts are roughly 3 digits
                parts = result.split(',')
                valid_format = True
                
                # First part can be any length, but subsequent parts should be 3 digits
                for i, part in enumerate(parts):
                    if i > 0 and len(part) != 3:
                        valid_format = False
                        break
                
                if valid_format:
                    # This looks like a properly formatted number with commas
                    # Remove commas and add to processed results
                    processed = result.replace(',', '')
                    processed_results.append(processed)
            
            # Also add the version without commas
            processed = result.replace(',', '').replace('.', '')
            processed_results.append(processed)
        
        # Special handling for numbers that might be misread
        for i, result in enumerate(processed_results):
            # Check for common OCR errors in this game's numbers
            
            # 1. Missing leading digit in large numbers (e.g., "095,477" should be "1,095,477")
            if len(result) >= 6 and result.startswith('0'):
                processed_results.append('1' + result[1:])
            
            # 2. Misreading 7 as 1 - only apply this selectively
            # Instead of replacing all 1s with 7s, only do it for specific patterns
            if '1' in result:
                # Only replace 1 with 7 in specific positions or patterns
                # For example, if 1 is followed by 9, it's often a misread 7
                if '19' in result:
                    alt_result = result.replace('19', '79')
                    processed_results.append(alt_result)
                
                # Or if 1 is at the beginning of a number and followed by certain digits
                if len(result) > 1 and result[0] == '1' and result[1] in '0123456':
                    alt_pos = '7' + result[1:]
                    processed_results.append(alt_pos)
                    
                # Don't do a blanket replacement of all 1s with 7s
        
        # Special handling for T4 troops with smaller numbers (2000-4000 range)
        # Check if any result starts with 1 and is followed by a 2 or 3 (like 12672 or 13268)
        for result in list(processed_results):  # Create a copy to avoid modifying during iteration
            if len(result) == 5 and result.startswith('1') and result[1] in '23':
                # This might be a case where OCR added a leading 1 to a 4-digit number
                # Add the version without the leading 1 as a candidate
                processed_results.append(result[1:])
                
        # Special handling for numbers around 20,000 (like 20055)
        for result in list(processed_results):
            if len(result) == 6 and result.startswith('1') and result[1] == '2' and result[2] == '0':
                # This might be a case where OCR added a leading 1 to a 5-digit number starting with 2
                # Add the version without the leading 1 as a candidate
                processed_results.append(result[1:])
                
        # Special handling for missing first digit in 6-digit numbers (like 20132 should be 270132)
        for result in list(processed_results):
            if len(result) == 5 and result.startswith('2') and result[1] == '0':
                # This might be a case where OCR missed the first digit (7) in a 6-digit number
                # Add versions with different possible first digits
                processed_results.append('7' + result)  # Add 7 as first digit (common for this game)
                processed_results.append('2' + result)  # Add 2 as first digit
                
        # NEW: Special handling for T5 Cavalry numbers (like 21824 should be 621424)
        for result in list(processed_results):
            if len(result) == 5 and result.startswith('2') and result[1] == '1' and result[2] == '8':
                # This might be a case where OCR missed the first digit (6) and misread other digits
                # Add versions with corrected digits
                processed_results.append('6' + result[0:1] + '14' + result[3:])  # Add 6 as first digit and correct middle digits
                processed_results.append('62' + '14' + result[3:])  # More aggressive correction
                
            # Also check for other patterns that might be misread T5 Cavalry numbers
            if len(result) == 5 and 20000 <= int(result) <= 30000:
                # This might be a misread of a 6-digit number in the 600,000 range
                processed_results.append('6' + result)
        
        # Choose the best result based on frequency and length
        from collections import Counter
        
        # Filter out results that are too short (likely incomplete readings)
        min_length = 4  # Reduced from 5 to catch 4-digit numbers like 2672 and 3268
        valid_results = [r for r in processed_results if len(r) >= min_length]
        
        if not valid_results:
            valid_results = processed_results  # Fall back to all results if none meet length criteria
        
        # Count occurrences of each result
        result_counts = Counter(valid_results)
        
        # Get the most common results
        most_common = result_counts.most_common(3)
        
        # Debug output
        print(f"OCR candidates for amount: {most_common}")
        
        if most_common:
            # NEW: Special case for T5 Cavalry numbers around 621,000
            six_digit_candidates_t5_cav = [r for r, count in most_common if len(r) == 6 and 620000 <= int(r) <= 622000]
            if six_digit_candidates_t5_cav:
                return six_digit_candidates_t5_cav[0]
                
            # Special case for 6-digit numbers around 270,000
            six_digit_candidates = [r for r, count in most_common if len(r) == 6 and 270000 <= int(r) <= 280000]
            if six_digit_candidates:
                return six_digit_candidates[0]
                
            # Special case for 5-digit numbers around 20,000
            five_digit_candidates = [r for r, count in most_common if len(r) == 5 and 20000 <= int(r) <= 30000]
            if five_digit_candidates:
                return five_digit_candidates[0]
                
            # Special case for T4 troops with 4-digit numbers (2000-4000 range)
            four_digit_candidates = [r for r, count in most_common if len(r) == 4 and 2000 <= int(r) <= 4000]
            if four_digit_candidates:
                return four_digit_candidates[0]
            
            # Special case for numbers that should have 7 digits (like 1,095,477)
            # Check if any of the candidates has 7 digits, which is likely the full number with commas
            seven_digit_candidates = [r for r, count in most_common if len(r) == 7]
            if seven_digit_candidates:
                return seven_digit_candidates[0]
            
            # If there's a clear winner (appears significantly more often)
            if len(most_common) > 1 and most_common[0][1] > most_common[1][1] * 1.5:
                return most_common[0][0]
            
            # Otherwise, prefer the longest among the most common results
            # This helps with cases where a number is partially read (e.g., "09547" vs "1095477")
            longest = max(most_common, key=lambda x: len(x[0]))
            return longest[0]
        
        # If no clear pattern, return the longest result
        return max(processed_results, key=len) if processed_results else '0'


class Spreadsheet():
    def __init__(self) -> None:
        self.filename = SPREADSHEET_NAME
        self.spreadsheet = sa.open(self.filename)
        self.main_worksheet = self.spreadsheet.worksheet(SHEET_NAME)
        
        # Check if headers exist, if not add them
        if self.main_worksheet.row_count == 0 or self.main_worksheet.acell('A1').value is None:
            self.initialize_headers()
    
    def initialize_headers(self):
        """
        Initialize the spreadsheet with headers for better readability.
        """
        # Use centralized configuration for consistent headers
        headers = get_spreadsheet_headers()

        # Update the first row with headers
        self.main_worksheet.update('A1:X1', [headers])
        
        # Format the header row to make it stand out
        self.main_worksheet.format('A1:X1', {
            'textFormat': {'bold': True},
            'backgroundColor': {'red': 0.9, 'green': 0.9, 'blue': 0.9}
        })
    
    def register_stats(self, troop_data, player_id):
        """
        Register stats in the spreadsheet.

        Args:
            troop_data (list): List of dead troop counts (T1 to T5 for all types).
            player_id (int): Player's ID.
        """
        player_id_str = str(player_id)
        id_cell = self.main_worksheet.find(player_id_str)

        if id_cell:
            # Update row if player exists
            self.main_worksheet.update(f"A{id_cell.row}:X{id_cell.row}", [[player_id_str] + troop_data])
        else:
            # Append a new row
            self.main_worksheet.append_row([player_id_str] + troop_data)


    
