"""
Discord Webhook Logging System for Hall of Heroes Bot
Replaces console logging with professional Discord notifications
"""

import asyncio
import aiohttp
import json
import time
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum
from dataclasses import dataclass
from collections import deque
import threading

from config import ERROR_WEBHOOK_URL, STATUS_WEBHOOK_URL, REVIEW_WEBHOOK_URL

class LogLevel(Enum):
    """Log levels for webhook notifications"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class WebhookType(Enum):
    """Types of webhook notifications"""
    ERROR = "error"
    STATUS = "status"
    REVIEW = "review"

@dataclass
class WebhookMessage:
    """Data class for webhook messages"""
    level: LogLevel
    message: str
    webhook_type: WebhookType
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None
    embed_color: Optional[int] = None

class WebhookLogger:
    """
    Discord webhook logger with rate limiting and error handling
    """
    
    def __init__(self):
        self._session: Optional[aiohttp.ClientSession] = None
        self._message_queue = deque()
        self._rate_limits = {
            WebhookType.ERROR: {"last_sent": 0, "interval": 5},    # 5 seconds between error messages
            WebhookType.STATUS: {"last_sent": 0, "interval": 30},  # 30 seconds between status messages
            WebhookType.REVIEW: {"last_sent": 0, "interval": 10},  # 10 seconds between review messages
        }
        self._lock = threading.Lock()
        self._enabled = True
        
        # Color scheme for different log levels
        self._colors = {
            LogLevel.DEBUG: 0x808080,      # Gray
            LogLevel.INFO: 0x00FF00,       # Green
            LogLevel.WARNING: 0xFFFF00,    # Yellow
            LogLevel.ERROR: 0xFF0000,      # Red
            LogLevel.CRITICAL: 0x8B0000,   # Dark Red
        }
        
        # Webhook URLs
        self._webhook_urls = {
            WebhookType.ERROR: ERROR_WEBHOOK_URL,
            WebhookType.STATUS: STATUS_WEBHOOK_URL,
            WebhookType.REVIEW: REVIEW_WEBHOOK_URL,
        }
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self._session is None or self._session.closed:
            self._session = aiohttp.ClientSession()
        return self._session
    
    def _should_send(self, webhook_type: WebhookType) -> bool:
        """Check if message should be sent based on rate limiting"""
        with self._lock:
            now = time.time()
            rate_limit = self._rate_limits[webhook_type]
            
            if now - rate_limit["last_sent"] >= rate_limit["interval"]:
                rate_limit["last_sent"] = now
                return True
            return False
    
    def _create_embed(self, message: WebhookMessage) -> Dict[str, Any]:
        """Create Discord embed for the message"""
        color = message.embed_color or self._colors.get(message.level, 0x000000)
        
        embed = {
            "title": f"🤖 Hall of Heroes Bot - {message.level.value}",
            "description": message.message,
            "color": color,
            "timestamp": message.timestamp.isoformat(),
            "footer": {
                "text": "Hall of Heroes Monitoring System"
            }
        }
        
        # Add metadata as fields if present
        if message.metadata:
            embed["fields"] = []
            for key, value in message.metadata.items():
                embed["fields"].append({
                    "name": key.replace("_", " ").title(),
                    "value": str(value)[:1024],  # Discord field value limit
                    "inline": True
                })
        
        return embed
    
    async def _send_webhook(self, webhook_url: str, embed: Dict[str, Any]) -> bool:
        """Send webhook message to Discord"""
        if not webhook_url:
            return False
        
        try:
            session = await self._get_session()
            payload = {
                "embeds": [embed],
                "username": "Hall of Heroes Bot",
                "avatar_url": "https://media.discordapp.net/attachments/1313178325291761677/1315091550652334142/IMG_3625.png"
            }
            
            async with session.post(webhook_url, json=payload) as response:
                if response.status == 204:
                    return True
                elif response.status == 429:  # Rate limited
                    logging.warning(f"Webhook rate limited: {response.status}")
                    return False
                else:
                    logging.error(f"Webhook failed: {response.status}")
                    return False
                    
        except Exception as e:
            logging.error(f"Failed to send webhook: {str(e)}")
            return False
    
    async def send_message(self, level: LogLevel, message: str, 
                          webhook_type: WebhookType = WebhookType.STATUS,
                          metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Send a message via Discord webhook
        
        Args:
            level: Log level
            message: Message content
            webhook_type: Type of webhook to use
            metadata: Additional metadata to include
            
        Returns:
            bool: True if message was sent successfully
        """
        if not self._enabled:
            return False
        
        # Check rate limiting
        if not self._should_send(webhook_type):
            return False
        
        webhook_url = self._webhook_urls.get(webhook_type)
        if not webhook_url:
            return False
        
        webhook_message = WebhookMessage(
            level=level,
            message=message,
            webhook_type=webhook_type,
            timestamp=datetime.now(),
            metadata=metadata
        )
        
        embed = self._create_embed(webhook_message)
        return await self._send_webhook(webhook_url, embed)
    
    def log_error(self, message: str, error: Optional[Exception] = None,
                  metadata: Optional[Dict[str, Any]] = None):
        """Log an error message (synchronous wrapper)"""
        error_metadata = metadata or {}
        if error:
            error_metadata.update({
                "error_type": type(error).__name__,
                "error_message": str(error)
            })

        # Always use console logging to avoid async issues
        logging.error(f"ERROR: {message} - {error_metadata}")
    
    def log_warning(self, message: str, metadata: Optional[Dict[str, Any]] = None):
        """Log a warning message (synchronous wrapper)"""
        # Always use console logging to avoid async issues
        logging.warning(f"WARNING: {message} - {metadata}")

    def log_info(self, message: str, metadata: Optional[Dict[str, Any]] = None):
        """Log an info message (synchronous wrapper)"""
        # Always use console logging to avoid async issues
        logging.info(f"INFO: {message} - {metadata}")

    def log_critical(self, message: str, error: Optional[Exception] = None,
                    metadata: Optional[Dict[str, Any]] = None):
        """Log a critical message (synchronous wrapper)"""
        error_metadata = metadata or {}
        if error:
            error_metadata.update({
                "error_type": type(error).__name__,
                "error_message": str(error)
            })

        # Always use console logging to avoid async issues
        logging.critical(f"CRITICAL: {message} - {error_metadata}")
    
    async def send_review_request(self, image_data: Dict[str, Any], 
                                 confidence_scores: Dict[str, float]) -> bool:
        """
        Send a manual review request
        
        Args:
            image_data: Information about the processed image
            confidence_scores: Confidence scores for detected elements
            
        Returns:
            bool: True if review request was sent successfully
        """
        message = "🔍 **Manual Review Required**\n\nLow confidence detection needs human verification."
        
        metadata = {
            "image_id": image_data.get("message_id", "unknown"),
            "user": image_data.get("user", "unknown"),
            "avg_confidence": f"{sum(confidence_scores.values()) / len(confidence_scores):.2f}" if confidence_scores else "0.00",
            "low_confidence_items": ", ".join([k for k, v in confidence_scores.items() if v < 0.7])
        }
        
        return await self.send_message(
            LogLevel.WARNING, 
            message, 
            WebhookType.REVIEW, 
            metadata
        )
    
    def enable(self):
        """Enable webhook logging"""
        self._enabled = True
    
    def disable(self):
        """Disable webhook logging"""
        self._enabled = False
    
    async def close(self):
        """Close the webhook logger and cleanup resources"""
        if self._session and not self._session.closed:
            await self._session.close()

# Global webhook logger instance
webhook_logger = WebhookLogger()

# Convenience functions
def log_error(message: str, error: Optional[Exception] = None, 
              metadata: Optional[Dict[str, Any]] = None):
    """Log an error via webhook"""
    webhook_logger.log_error(message, error, metadata)

def log_warning(message: str, metadata: Optional[Dict[str, Any]] = None):
    """Log a warning via webhook"""
    webhook_logger.log_warning(message, metadata)

def log_info(message: str, metadata: Optional[Dict[str, Any]] = None):
    """Log info via webhook"""
    webhook_logger.log_info(message, metadata)

def log_critical(message: str, error: Optional[Exception] = None,
                metadata: Optional[Dict[str, Any]] = None):
    """Log critical error via webhook"""
    webhook_logger.log_critical(message, error, metadata)

async def send_review_request(image_data: Dict[str, Any], 
                             confidence_scores: Dict[str, float]) -> bool:
    """Send manual review request via webhook"""
    return await webhook_logger.send_review_request(image_data, confidence_scores)
